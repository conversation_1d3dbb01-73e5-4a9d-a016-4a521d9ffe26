import { Controller } from '@hotwired/stimulus';

// Données de test intégrées pour éviter les problèmes d'import
const COUNTRIES_DATA = {
    'France': 'EURO',
    'Allemagne': 'EURO',
    'Espagne': 'EURO',
    'Italie': 'EURO',
    'Japon': 'HORS_EURO',
    'États-Unis': 'HORS_EURO',
    'Royaume-Uni': 'HORS_EURO',
    'Canada': 'HORS_EURO',
    'Australie': 'HORS_EURO',
    'Brésil': 'HORS_EURO',
    'Chine': 'HORS_EURO',
    'Inde': 'HORS_EURO',
    'Russie': 'HORS_EURO',
    'Mexique': 'HORS_EURO',
    'Argentine': 'HORS_EURO'
};

function getCountriesList() {
    return Object.keys(COUNTRIES_DATA).sort();
}

function getCountryZone(country) {
    return COUNTRIES_DATA[country] || null;
}

function filterCountries(search) {
    if (!search) return getCountriesList();

    const searchLower = search.toLowerCase();
    return getCountriesList().filter(country =>
        country.toLowerCase().includes(searchLower)
    );
}

/**
 * Controller Stimulus pour l'auto-complétion des pays avec pré-remplissage de la zone
 */
export default class extends Controller {
    static targets = ['input'];
    static values = {
        placeholder: String,
        zoneTarget: String
    };

    connect() {
        console.log('Country autocomplete controller connected');
        this.isOpen = false;
        this.selectedIndex = -1;
        this.filteredCountries = [];

        // Récupérer l'élément zone (input caché du dropdown personnalisé)
        if (this.hasZoneTargetValue) {
            this.zoneElement = document.querySelector(this.zoneTargetValue);
            console.log('Zone element found:', this.zoneElement);
        }

        // Créer le dropdown
        this.createDropdown();

        this.setupEventListeners();
    }

    disconnect() {
        this.removeEventListeners();
    }

    /**
     * Crée le dropdown d'auto-complétion
     */
    createDropdown() {
        // Créer le dropdown
        this.dropdown = document.createElement('div');
        this.dropdown.className = 'absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto hidden';

        // Insérer après l'input
        this.inputTarget.parentNode.appendChild(this.dropdown);
        console.log('Dropdown created and added');
    }

    /**
     * Configure les event listeners
     */
    setupEventListeners() {
        this.inputTarget.addEventListener('input', this.handleInput.bind(this));
        this.inputTarget.addEventListener('focus', this.handleFocus.bind(this));
        this.inputTarget.addEventListener('blur', this.handleBlur.bind(this));
        this.inputTarget.addEventListener('keydown', this.handleKeydown.bind(this));

        // Listener pour les clics en dehors
        document.addEventListener('click', this.handleDocumentClick.bind(this));
    }

    /**
     * Supprime les event listeners
     */
    removeEventListeners() {
        document.removeEventListener('click', this.handleDocumentClick.bind(this));
    }

    /**
     * Gère la saisie dans l'input
     */
    handleInput(event) {
        console.log('Input event triggered:', event.target.value);
        const value = event.target.value;
        this.filteredCountries = filterCountries(value);
        this.selectedIndex = -1;
        this.updateDropdown();
        this.showDropdown();

        // Mettre à jour la zone si le pays est reconnu
        if (this.zoneElement) {
            const zone = getCountryZone(value);
            if (zone) {
                this.updateZone(zone, true);
                console.log('Zone updated to:', zone);
            } else if (value.length > 0) {
                // Ne pas réinitialiser immédiatement, attendre que l'utilisateur finisse de taper
                clearTimeout(this.zoneResetTimeout);
                this.zoneResetTimeout = setTimeout(() => {
                    if (!getCountryZone(this.inputTarget.value)) {
                        this.updateZone('', false);
                    }
                }, 1000);
            } else {
                // Champ vide, désactiver le dropdown Zone
                this.updateZone('', false);
            }
        }
    }

    /**
     * Gère le focus sur l'input
     */
    handleFocus(event) {
        if (this.inputTarget.value.length === 0) {
            this.filteredCountries = getCountriesList();
            this.updateDropdown();
        }
        this.showDropdown();
    }

    /**
     * Gère la perte de focus
     */
    handleBlur(event) {
        // Délai pour permettre le clic sur une option
        setTimeout(() => {
            this.hideDropdown();
        }, 150);
    }

    /**
     * Gère les touches du clavier
     */
    handleKeydown(event) {
        if (!this.isOpen) return;

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, this.filteredCountries.length - 1);
                this.updateSelection();
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                this.updateSelection();
                break;
            case 'Enter':
                event.preventDefault();
                if (this.selectedIndex >= 0) {
                    this.selectCountry(this.filteredCountries[this.selectedIndex]);
                }
                break;
            case 'Escape':
                this.hideDropdown();
                break;
        }
    }

    /**
     * Gère les clics en dehors du composant
     */
    handleDocumentClick(event) {
        if (!this.element.contains(event.target)) {
            this.hideDropdown();
        }
    }

    /**
     * Met à jour le contenu du dropdown
     */
    updateDropdown() {
        if (!this.dropdown) return;

        this.dropdown.innerHTML = '';

        if (this.filteredCountries.length === 0) {
            const noResult = document.createElement('div');
            noResult.className = 'px-4 py-2 text-gray-500 text-sm';
            noResult.textContent = 'Aucun pays trouvé';
            this.dropdown.appendChild(noResult);
            return;
        }

        this.filteredCountries.forEach((country, index) => {
            const option = document.createElement('div');
            option.className = 'px-4 py-2 cursor-pointer hover:bg-blue-50 text-sm flex justify-between items-center';
            option.dataset.index = index;

            const countryName = document.createElement('span');
            countryName.textContent = country;
            option.appendChild(countryName);

            const zone = getCountryZone(country);
            if (zone) {
                const zoneBadge = document.createElement('span');
                zoneBadge.className = `px-2 py-1 text-xs rounded-full ${
                    zone === 'EURO'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                }`;
                zoneBadge.textContent = zone === 'EURO' ? 'Zone Euro' : 'Hors Zone Euro';
                option.appendChild(zoneBadge);
            }

            option.addEventListener('click', () => {
                this.selectCountry(country);
            });

            this.dropdown.appendChild(option);
        });
    }

    /**
     * Met à jour la sélection visuelle
     */
    updateSelection() {
        if (!this.dropdown) return;

        const options = this.dropdown.querySelectorAll('[data-index]');
        options.forEach((option, index) => {
            if (index === this.selectedIndex) {
                option.classList.add('bg-blue-100');
            } else {
                option.classList.remove('bg-blue-100');
            }
        });

        // Scroll vers l'élément sélectionné
        if (this.selectedIndex >= 0 && options[this.selectedIndex]) {
            options[this.selectedIndex].scrollIntoView({
                block: 'nearest'
            });
        }
    }

    /**
     * Sélectionne un pays
     */
    selectCountry(country) {
        this.inputTarget.value = country;
        this.inputTarget.dispatchEvent(new Event('input'));
        this.inputTarget.dispatchEvent(new Event('change'));

        // Mettre à jour la zone
        if (this.zoneElement) {
            const zone = getCountryZone(country);
            if (zone) {
                this.updateZone(zone, true);
            }
        }

        this.hideDropdown();
    }

    /**
     * Affiche le dropdown
     */
    showDropdown() {
        if (!this.dropdown || this.filteredCountries.length === 0) return;

        this.dropdown.classList.remove('hidden');
        this.isOpen = true;
        console.log('Dropdown shown');
    }

    /**
     * Cache le dropdown
     */
    hideDropdown() {
        if (!this.dropdown) return;

        this.dropdown.classList.add('hidden');
        this.isOpen = false;
        this.selectedIndex = -1;
        console.log('Dropdown hidden');
    }

    /**
     * Met à jour la zone (compatible avec les dropdowns personnalisés et les selects classiques)
     */
    updateZone(zone, enabled) {
        if (!this.zoneElement) return;

        // Chercher le dropdown personnalisé parent
        const zoneDropdownElement = this.zoneElement.closest('[data-controller*="custom-dropdown"]');

        if (zoneDropdownElement) {
            // C'est un dropdown personnalisé, envoyer un événement de suggestion
            const event = new CustomEvent('zone:suggest', {
                detail: { zone, enabled },
                bubbles: true
            });
            zoneDropdownElement.dispatchEvent(event);
        } else {
            // C'est un select classique, mise à jour directe
            this.zoneElement.value = zone;
            this.zoneElement.dispatchEvent(new Event('change'));

            if (enabled) {
                this.zoneElement.disabled = false;
                this.zoneElement.classList.remove('bg-gray-50');
            } else {
                this.zoneElement.disabled = true;
                this.zoneElement.classList.add('bg-gray-50');
            }
        }
    }
}
