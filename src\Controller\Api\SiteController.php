<?php

namespace App\Controller\Api;

use App\Entity\Site;
use App\Repository\SiteRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/sites', name: 'api_site_')]
class SiteController extends AbstractController
{
    public function __construct(
        private SiteRepository $siteRepository
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $clientId = $request->query->get('client');
        $search = $request->query->get('search');
        $actif = $request->query->get('actif');

        if ($clientId) {
            $sites = $this->siteRepository->findByClient($clientId);
        } elseif ($search) {
            $sites = $this->siteRepository->findByNom($search);
        } elseif ($actif !== null) {
            $sites = $this->siteRepository->findActifs();
        } else {
            $sites = $this->siteRepository->findAll();
        }

        return $this->json([
            'data' => $sites,
            'total' => count($sites)
        ], Response::HTTP_OK, [], [
            'groups' => ['site:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(Site $site): JsonResponse
    {
        return $this->json($site, Response::HTTP_OK, [], [
            'groups' => ['site:read', 'site:detail']
        ]);
    }
}
