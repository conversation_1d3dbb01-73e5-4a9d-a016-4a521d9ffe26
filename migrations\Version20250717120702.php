<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250717120702 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Cette migration vérifie l'existence des tables avant de les créer
        if (!$schema->hasTable('client')) {
            $this->addSql('CREATE TABLE client (id INT AUTO_INCREMENT NOT NULL, nom VARCHAR(255) NOT NULL, raison_sociale VARCHAR(255) DEFAULT NULL, siret VARCHAR(20) DEFAULT NULL, email VARCHAR(180) DEFAULT NULL, telephone VARCHAR(20) DEFAULT NULL, adresse LONGTEXT DEFAULT NULL, code_postal VARCHAR(10) DEFAULT NULL, ville VARCHAR(100) DEFAULT NULL, pays VARCHAR(100) DEFAULT NULL, actif TINYINT(1) DEFAULT NULL, notes LONGTEXT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        }
        if (!$schema->hasTable('mission_user')) {
            $this->addSql('CREATE TABLE mission_user (mission_id INT NOT NULL, user_id INT NOT NULL, INDEX IDX_A4D17A46BE6CAE90 (mission_id), INDEX IDX_A4D17A46A76ED395 (user_id), PRIMARY KEY(mission_id, user_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        }
        if (!$schema->hasTable('site')) {
            $this->addSql('CREATE TABLE site (id INT AUTO_INCREMENT NOT NULL, client_id INT NOT NULL, nom VARCHAR(255) NOT NULL, adresse LONGTEXT DEFAULT NULL, code_postal VARCHAR(10) DEFAULT NULL, ville VARCHAR(100) DEFAULT NULL, pays VARCHAR(100) DEFAULT NULL, telephone VARCHAR(20) DEFAULT NULL, email VARCHAR(180) DEFAULT NULL, actif TINYINT(1) DEFAULT NULL, notes LONGTEXT DEFAULT NULL, INDEX IDX_694309E419EB6921 (client_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        }
        if (!$schema->hasTable('user')) {
            $this->addSql('CREATE TABLE `user` (id INT AUTO_INCREMENT NOT NULL, nom VARCHAR(100) NOT NULL, prenom VARCHAR(100) NOT NULL, email VARCHAR(180) NOT NULL, role VARCHAR(50) NOT NULL, horaire_hebdo DOUBLE PRECISION NOT NULL, forfait_jour TINYINT(1) DEFAULT NULL, telephone VARCHAR(20) DEFAULT NULL, date_embauche DATE DEFAULT NULL, date_depart DATE DEFAULT NULL, actif TINYINT(1) DEFAULT NULL, notes LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_8D93D649E7927C74 (email), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        }
        // Cette migration est obsolète car les tables et contraintes existent déjà
        // Ne rien faire pour éviter les erreurs de duplication
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mission DROP FOREIGN KEY FK_9067F23C19EB6921');
        $this->addSql('ALTER TABLE mission DROP FOREIGN KEY FK_9067F23CF6BD1646');
        $this->addSql('ALTER TABLE segment DROP FOREIGN KEY FK_1881F565A76ED395');
        $this->addSql('ALTER TABLE semaine_travail DROP FOREIGN KEY FK_4104D40BA76ED395');
        $this->addSql('ALTER TABLE mission_user DROP FOREIGN KEY FK_A4D17A46BE6CAE90');
        $this->addSql('ALTER TABLE mission_user DROP FOREIGN KEY FK_A4D17A46A76ED395');
        $this->addSql('ALTER TABLE site DROP FOREIGN KEY FK_694309E419EB6921');
        $this->addSql('DROP TABLE client');
        $this->addSql('DROP TABLE mission_user');
        $this->addSql('DROP TABLE site');
        $this->addSql('DROP TABLE `user`');
        $this->addSql('DROP INDEX IDX_9067F23C19EB6921 ON mission');
        $this->addSql('DROP INDEX IDX_9067F23CF6BD1646 ON mission');
        $this->addSql('ALTER TABLE mission DROP client_id, DROP site_id, CHANGE collaborateur_id collaborateur_id INT NOT NULL');
        $this->addSql('DROP INDEX IDX_1881F565A76ED395 ON segment');
        $this->addSql('ALTER TABLE segment DROP user_id');
        $this->addSql('DROP INDEX IDX_4104D40BA76ED395 ON semaine_travail');
        $this->addSql('ALTER TABLE semaine_travail DROP user_id, CHANGE collaborateur_id collaborateur_id INT NOT NULL');
    }
}
