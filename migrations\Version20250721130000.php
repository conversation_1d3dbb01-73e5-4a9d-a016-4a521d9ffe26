<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Ajout du champ user_osi à la table user
 */
final class Version20250721130000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute le champ user_osi à la table user pour gérer l\'affichage des utilisateurs OSI';
    }

    public function up(Schema $schema): void
    {
        // Ajouter le champ user_osi seulement s'il n'existe pas
        if ($schema->hasTable('user') && !$schema->getTable('user')->hasColumn('user_osi')) {
            $this->addSql('ALTER TABLE `user` ADD user_osi TINYINT(1) DEFAULT 0');
            // Mettre tous les utilisateurs existants comme utilisateurs OSI par défaut
            $this->addSql('UPDATE `user` SET user_osi = 1 WHERE user_osi IS NULL');
        }
    }

    public function down(Schema $schema): void
    {
        // Supprimer le champ user_osi
        $this->addSql('ALTER TABLE `user` DROP user_osi');
    }
}
