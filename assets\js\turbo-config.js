/**
 * Configuration Turbo pour réduire les warnings d'import map
 */

// Réduire les logs Turbo en production
if (window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1')) {
    // Désactiver les warnings d'import map en production
    const originalWarn = console.warn;
    console.warn = function(...args) {
        const message = args.join(' ');
        if (message.includes('import map rule') || message.includes('conflicted with')) {
            return; // Ignorer ces warnings spécifiques
        }
        originalWarn.apply(console, args);
    };
}

// Configuration Turbo pour améliorer les performances
document.addEventListener('turbo:before-cache', () => {
    // Nettoyer les ressources avant la mise en cache
    if (window.loadingSpinner) {
        window.loadingSpinner.hide();
    }
});

document.addEventListener('turbo:before-visit', () => {
    // Préparer la navigation
    console.log('Navigation Turbo en cours...');
});

document.addEventListener('turbo:visit-end', () => {
    // Navigation terminée
    console.log('Navigation Turbo terminée');
});

export default {
    // Configuration exportée si nécessaire
    configured: true
};
