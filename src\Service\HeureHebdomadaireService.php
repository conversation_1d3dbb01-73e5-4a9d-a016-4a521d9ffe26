<?php

namespace App\Service;

use App\Entity\User;
use App\Entity\Segment;
use App\Repository\SegmentRepository;

/**
 * Service pour calculer les heures hebdomadaires directement depuis les segments
 * Remplace la table SemaineTravail
 */
class HeureHebdomadaireService
{
    public function __construct(
        private SegmentRepository $segmentRepository,
        private HeureCalculatorService $heureCalculator
    ) {}

    /**
     * Calcule les heures d'un utilisateur pour une semaine donnée
     */
    public function calculerHeuresSemaine(User $user, string $semaineAnnee): array
    {
        // Convertir la semaine en dates
        [$annee, $semaine] = explode('-', $semaineAnnee);
        $dateDebut = new \DateTime();
        $dateDebut->setISODate((int)$annee, (int)$semaine, 1); // Lundi
        $dateFin = clone $dateDebut;
        $dateFin->modify('+6 days'); // Dimanche

        // Récupérer les segments de la semaine
        $segments = $this->segmentRepository->findByUserAndPeriod($user, $dateDebut, $dateFin);

        // Calculer les heures totales
        $heuresSaisies = 0;
        foreach ($segments as $segment) {
            $heuresSaisies += $this->heureCalculator->getDureeHeures($segment);
        }

        // Calculer les heures normales et supplémentaires
        $horaireHebdo = $user->getHoraireHebdo();
        $heuresNormales = min($heuresSaisies, $horaireHebdo);
        $heuresSupplementaires = max(0, $heuresSaisies - $horaireHebdo);

        // Calcul des majorations (comme dans HeureCalculatorService)
        $heuresSupp25 = 0;
        $heuresSupp50 = 0;
        $seuilMajoration50 = 43;

        if ($heuresSupplementaires > 0) {
            $totalHeures = $heuresNormales + $heuresSupplementaires;
            if ($totalHeures > $seuilMajoration50) {
                $heuresSupp50 = $totalHeures - $seuilMajoration50;
                $heuresSupp25 = $heuresSupplementaires - $heuresSupp50;
            } else {
                $heuresSupp25 = $heuresSupplementaires;
            }
        }

        return [
            'semaineAnnee' => $semaineAnnee,
            'user' => [
                'id' => $user->getId(),
                'nomComplet' => $user->getNomComplet(),
                'horaireHebdo' => $user->getHoraireHebdo()
            ],
            'segments' => array_map(function($segment) {
                $mission = $segment->getMission();
                return [
                    'id' => $segment->getId(),
                    'type' => $segment->getType(),
                    'dateHeureDebut' => $segment->getDateHeureDebut()?->format('Y-m-d H:i:s'),
                    'dateHeureFin' => $segment->getDateHeureFin()?->format('Y-m-d H:i:s'),
                    'mission' => $mission ? [
                        'id' => $mission->getId(),
                        'titre' => $mission->getTitre(),
                        'pays' => $mission->getPays()
                    ] : null
                ];
            }, $segments),
            'heuresSaisies' => round($heuresSaisies, 2),
            'heuresNormales' => round($heuresNormales, 2),
            'heuresSupplementaires' => round($heuresSupplementaires, 2),
            'heuresSupp25' => round($heuresSupp25, 2),
            'heuresSupp50' => round($heuresSupp50, 2),
            'totalHeures' => round($heuresSaisies, 2),
            'horaireContractuel' => $horaireHebdo,
            'dateDebut' => $dateDebut,
            'dateFin' => $dateFin,
            'source' => 'segments' // Toujours calculé depuis les segments
        ];
    }

    /**
     * Calcule les heures pour une période (plusieurs semaines)
     */
    public function calculerHeuresPeriode(User $user, \DateTimeInterface $dateDebut, \DateTimeInterface $dateFin): array
    {
        $semaines = $this->getSemainesEntreDates($dateDebut, $dateFin);
        $detailSemaines = [];
        $totaux = [
            'heuresSaisies' => 0,
            'heuresNormales' => 0,
            'heuresSupplementaires' => 0,
            'heuresSupp25' => 0,
            'heuresSupp50' => 0
        ];

        foreach ($semaines as $semaineAnnee) {
            $calculSemaine = $this->calculerHeuresSemaine($user, $semaineAnnee);
            $detailSemaines[] = $calculSemaine;

            $totaux['heuresSaisies'] += $calculSemaine['heuresSaisies'];
            $totaux['heuresNormales'] += $calculSemaine['heuresNormales'];
            $totaux['heuresSupplementaires'] += $calculSemaine['heuresSupplementaires'];
            $totaux['heuresSupp25'] += $calculSemaine['heuresSupp25'];
            $totaux['heuresSupp50'] += $calculSemaine['heuresSupp50'];
        }

        return [
            'user' => [
                'id' => $user->getId(),
                'nomComplet' => $user->getNomComplet(),
                'horaireHebdo' => $user->getHoraireHebdo()
            ],
            'dateDebut' => $dateDebut->format('Y-m-d'),
            'dateFin' => $dateFin->format('Y-m-d'),
            'nombreSemaines' => count($semaines),
            'detailSemaines' => $detailSemaines,
            'totaux' => $totaux,
            'moyenneHebdomadaire' => count($semaines) > 0 ? $totaux['heuresSaisies'] / count($semaines) : 0
        ];
    }

    /**
     * Trouve les semaines avec heures supplémentaires
     */
    public function findSemainesAvecHeuresSupplementaires(User $user = null, \DateTimeInterface $dateDebut = null, \DateTimeInterface $dateFin = null): array
    {
        $dateDebut = $dateDebut ?? new \DateTime('-3 months');
        $dateFin = $dateFin ?? new \DateTime();

        $users = $user ? [$user] : $this->segmentRepository->findUsersWithSegments();
        $semainesAvecHS = [];

        foreach ($users as $userItem) {
            $calculPeriode = $this->calculerHeuresPeriode($userItem, $dateDebut, $dateFin);

            foreach ($calculPeriode['detailSemaines'] as $semaine) {
                if ($semaine['heuresSupplementaires'] > 0) {
                    $semainesAvecHS[] = $semaine;
                }
            }
        }

        // Trier par semaine décroissante
        usort($semainesAvecHS, function($a, $b) {
            return strcmp($b['semaineAnnee'], $a['semaineAnnee']);
        });

        return $semainesAvecHS;
    }

    /**
     * Génère la liste des semaines entre deux dates
     */
    private function getSemainesEntreDates(\DateTimeInterface $dateDebut, \DateTimeInterface $dateFin): array
    {
        $semaines = [];
        $current = clone $dateDebut;

        // Aller au lundi de la semaine de début
        $current->modify('monday this week');

        while ($current <= $dateFin) {
            $semaines[] = $current->format('Y-W');
            $current->modify('+1 week');
        }

        return $semaines;
    }

    /**
     * Obtient les statistiques hebdomadaires
     */
    public function getStatistiquesHebdomadaires(\DateTimeInterface $dateDebut = null, \DateTimeInterface $dateFin = null): array
    {
        $dateDebut = $dateDebut ?? new \DateTime('-3 months');
        $dateFin = $dateFin ?? new \DateTime();

        $users = $this->segmentRepository->findUsersWithSegments();
        $stats = [
            'nombreUsers' => count($users),
            'totalHeures' => 0,
            'totalHeuresSupplementaires' => 0,
            'nombreSemainesAvecHS' => 0,
            'moyenneHeuresParUser' => 0
        ];

        $totalSemaines = 0;

        foreach ($users as $user) {
            $calculPeriode = $this->calculerHeuresPeriode($user, $dateDebut, $dateFin);
            $stats['totalHeures'] += $calculPeriode['totaux']['heuresSaisies'];
            $stats['totalHeuresSupplementaires'] += $calculPeriode['totaux']['heuresSupplementaires'];
            $totalSemaines += $calculPeriode['nombreSemaines'];

            foreach ($calculPeriode['detailSemaines'] as $semaine) {
                if ($semaine['heuresSupplementaires'] > 0) {
                    $stats['nombreSemainesAvecHS']++;
                }
            }
        }

        $stats['moyenneHeuresParUser'] = count($users) > 0 ? $stats['totalHeures'] / count($users) : 0;

        return $stats;
    }
}
