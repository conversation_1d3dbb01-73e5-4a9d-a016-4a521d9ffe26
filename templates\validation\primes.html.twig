{% extends 'base.html.twig' %}

{% block title %}Validation des Primes{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Validation des Primes</h1>
                <div class="btn-group">
                    {% if user_role == 'responsable_mission' %}
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#genererPrimesModal">
                            <i class="fas fa-plus"></i> Générer des primes
                        </button>
                    {% endif %}
                    <a href="{{ path('app_validation_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                    </a>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.total }}</h4>
                                    <p class="card-text">Total primes</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-euro-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.en_attente_responsable }}</h4>
                                    <p class="card-text">En attente responsable</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-hourglass-half fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.en_attente_rh }}</h4>
                                    <p class="card-text">En attente RH</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-user-tie fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.validees }}</h4>
                                    <p class="card-text">Validées</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table des primes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        {% if user_role == 'responsable_mission' %}
                            Primes en attente de validation responsable
                        {% elseif user_role == 'assistante_rh' %}
                            Primes en attente de validation RH
                        {% else %}
                            Primes validées
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if primes|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Utilisateur</th>
                                        <th>Mission</th>
                                        <th>Période</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for prime in primes %}
                                        <tr>
                                            <td>{{ prime.user.nomComplet }}</td>
                                            <td>{{ prime.mission.titre }}</td>
                                            <td>
                                                {{ prime.periodeDebut|date('d/m/Y') }} - 
                                                {{ prime.periodeFin|date('d/m/Y') }}
                                            </td>
                                            <td>{{ prime.montantFloat|number_format(2, ',', ' ') }} €</td>
                                            <td>
                                                <span class="badge badge-{{ prime.statutValidation == 'Validée' ? 'success' : (prime.statutValidation == 'En attente validation RH' ? 'primary' : 'warning') }}">
                                                    {{ prime.statutValidation }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    {% if user_role == 'responsable_mission' and not prime.validationResponsableOsi %}
                                                        <button type="button" class="btn btn-success btn-valider-responsable" data-id="{{ prime.id }}">
                                                            <i class="fas fa-check"></i> Valider
                                                        </button>
                                                        <button type="button" class="btn btn-warning btn-recalculer" data-id="{{ prime.id }}">
                                                            <i class="fas fa-calculator"></i> Recalculer
                                                        </button>
                                                    {% elseif user_role == 'responsable_mission' and prime.validationResponsableOsi %}
                                                        <button type="button" class="btn btn-danger btn-invalider-responsable" data-id="{{ prime.id }}">
                                                            <i class="fas fa-times"></i> Invalider
                                                        </button>
                                                    {% elseif user_role == 'assistante_rh' and prime.validationResponsableOsi and not prime.validationAssistanteRh %}
                                                        <button type="button" class="btn btn-success btn-valider-rh" data-id="{{ prime.id }}">
                                                            <i class="fas fa-check"></i> Valider RH
                                                        </button>
                                                    {% elseif user_role == 'assistante_rh' and prime.validationAssistanteRh %}
                                                        <button type="button" class="btn btn-danger btn-invalider-rh" data-id="{{ prime.id }}">
                                                            <i class="fas fa-times"></i> Invalider RH
                                                        </button>
                                                    {% endif %}
                                                    <a href="{{ path('api_prime_detail', {id: prime.id}) }}" class="btn btn-info" target="_blank">
                                                        <i class="fas fa-eye"></i> Détail
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5>Aucune prime en attente</h5>
                            <p class="text-muted">
                                {% if user_role == 'responsable_mission' %}
                                    Toutes les primes ont été validées ou aucune prime n'a été générée.
                                {% elseif user_role == 'assistante_rh' %}
                                    Aucune prime n'est en attente de validation RH.
                                {% else %}
                                    Aucune prime validée disponible.
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour générer des primes -->
{% if user_role == 'responsable_mission' %}
<div class="modal fade" id="genererPrimesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Générer des primes</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="genererPrimesForm">
                    <div class="form-group">
                        <label for="debut">Date de début</label>
                        <input type="date" class="form-control" id="debut" required>
                    </div>
                    <div class="form-group">
                        <label for="fin">Date de fin</label>
                        <input type="date" class="form-control" id="fin" required>
                    </div>
                    <div class="form-group">
                        <label for="mission">Mission (optionnel)</label>
                        <select class="form-control" id="mission">
                            <option value="">Toutes les missions</option>
                            <!-- Les missions seront chargées via JavaScript -->
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="btnGenererPrimes">Générer</button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation par responsable
    document.querySelectorAll('.btn-valider-responsable').forEach(btn => {
        btn.addEventListener('click', function() {
            const primeId = this.dataset.id;
            validerPrimeResponsable(primeId);
        });
    });

    // Invalidation par responsable
    document.querySelectorAll('.btn-invalider-responsable').forEach(btn => {
        btn.addEventListener('click', function() {
            const primeId = this.dataset.id;
            invaliderPrimeResponsable(primeId);
        });
    });

    // Validation par RH
    document.querySelectorAll('.btn-valider-rh').forEach(btn => {
        btn.addEventListener('click', function() {
            const primeId = this.dataset.id;
            validerPrimeRh(primeId);
        });
    });

    // Invalidation par RH
    document.querySelectorAll('.btn-invalider-rh').forEach(btn => {
        btn.addEventListener('click', function() {
            const primeId = this.dataset.id;
            invaliderPrimeRh(primeId);
        });
    });

    // Recalcul
    document.querySelectorAll('.btn-recalculer').forEach(btn => {
        btn.addEventListener('click', function() {
            const primeId = this.dataset.id;
            recalculerPrime(primeId);
        });
    });

    function validerPrimeResponsable(id) {
        fetch(`{{ path('app_validation_prime_responsable', {id: '__ID__'}) }}`.replace('__ID__', id), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }

    function invaliderPrimeResponsable(id) {
        if (!confirm('Êtes-vous sûr de vouloir invalider cette prime ?')) {
            return;
        }

        fetch(`{{ path('app_validation_prime_responsable_invalider', {id: '__ID__'}) }}`.replace('__ID__', id), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }

    function validerPrimeRh(id) {
        fetch(`{{ path('app_validation_prime_rh', {id: '__ID__'}) }}`.replace('__ID__', id), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }

    function invaliderPrimeRh(id) {
        if (!confirm('Êtes-vous sûr de vouloir invalider cette prime ?')) {
            return;
        }

        fetch(`{{ path('app_validation_prime_rh_invalider', {id: '__ID__'}) }}`.replace('__ID__', id), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }

    function recalculerPrime(id) {
        if (!confirm('Êtes-vous sûr de vouloir recalculer cette prime ?')) {
            return;
        }

        fetch(`{{ path('app_validation_prime_recalculer', {id: '__ID__'}) }}`.replace('__ID__', id), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Prime recalculée: ' + data.nouveau_montant + '€');
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }
});
</script>
{% endblock %}
