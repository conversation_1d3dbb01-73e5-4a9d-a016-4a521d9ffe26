import { Controller } from '@hotwired/stimulus';

/**
 * Controller Stimulus pour la gestion des modals de missions
 */
export default class extends Controller {
    static targets = ['addModal', 'addForm', 'editModal'];
    static values = {
        clientsUrl: String,
        sitesUrl: String
    };

    connect() {
        this.allClients = [];
        this.allSites = [];
        this.setupFormHandler();
        this.loadClients();
    }

    disconnect() {
        // Nettoyer les ressources quand le contrôleur est déconnecté
        if (this.loadTimer) {
            clearTimeout(this.loadTimer);
        }
    }

    /**
     * Configure le gestionnaire de formulaire d'ajout
     */
    setupFormHandler() {
        if (this.hasAddFormTarget) {
            this.addFormTarget.addEventListener('submit', (e) => {
                e.preventDefault();
                this.dispatch('createMission', { detail: { form: e.target }, bubbles: true });
            });
        }

        // Écouter les changements de client
        this.setupClientChangeHandlers();
    }

    /**
     * Configure les gestionnaires de changement de client
     */
    setupClientChangeHandlers() {
        // Écouter les changements sur les inputs cachés des dropdowns
        const createClientInput = document.querySelector('[name="clientId"]');
        const editClientInput = document.querySelector('#editClientSelect');

        if (createClientInput) {
            createClientInput.addEventListener('change', (e) => {
                this.loadSitesForClient(e.target.value, 'siteSelect');
            });
        }

        if (editClientInput) {
            editClientInput.addEventListener('change', (e) => {
                this.loadSitesForClient(e.target.value, 'editSiteSelect');
            });
        }
    }



    /**
     * Ouvre le modal d'ajout
     */
    openAddModal() {
        if (this.hasAddModalTarget) {
            this.addModalTarget.classList.remove('hidden');
            // Déclencher le chargement des utilisateurs après un court délai
            setTimeout(() => {
                this.dispatch('loadUsers', { bubbles: true });
            }, 100);
        }
    }

    /**
     * Ouvre le modal d'édition
     */
    openEditModal() {
        if (this.hasEditModalTarget) {
            this.editModalTarget.classList.remove('hidden');

            // S'assurer que les clients sont chargés pour l'édition
            if (this.allClients.length === 0) {
                this.loadClients();
            }

            // Peupler les dropdowns clients pour l'édition
            setTimeout(() => {
                this.populateClientSelect('editClientSelect');
            }, 50);

            // Déclencher le chargement des utilisateurs pour l'édition après un court délai
            setTimeout(() => {
                this.dispatch('loadUsersForEdit', { bubbles: true });
            }, 100);
        }
    }

    /**
     * Ferme le modal d'ajout
     */
    closeAddModal() {
        if (this.hasAddModalTarget) {
            this.addModalTarget.classList.add('hidden');
        }
        if (this.hasAddFormTarget) {
            this.addFormTarget.reset();
        }
        this.dispatch('clearSelection', { bubbles: true });
    }

    /**
     * Ferme le modal d'édition
     */
    closeEditModal() {
        if (this.hasEditModalTarget) {
            this.editModalTarget.classList.add('hidden');
        }
    }

    /**
     * Ferme le modal d'ajout en cliquant sur le backdrop
     */
    closeAddModalOnBackdrop(event) {
        // Ne fermer que si on clique directement sur le backdrop (pas sur ses enfants)
        if (this.hasAddModalTarget && event.target === event.currentTarget) {
            event.stopPropagation();
            this.closeAddModal();
        }
    }

    /**
     * Ferme le modal d'édition en cliquant sur le backdrop
     */
    closeEditModalOnBackdrop(event) {
        // Ne fermer que si on clique directement sur le backdrop (pas sur ses enfants)
        if (this.hasEditModalTarget && event.target === event.currentTarget) {
            event.stopPropagation();
            this.closeEditModal();
        }
    }



    /**
     * Charge les clients depuis l'API
     */
    async loadClients() {
        try {
            const response = await window.ajax.get(this.clientsUrlValue);

            // Gérer les différents formats de réponse
            const payload = response.data ?? response;
            let clients;

            if (Array.isArray(payload)) {
                clients = payload;
            } else if (Array.isArray(payload['hydra:member'])) {
                clients = payload['hydra:member'];
            } else if (Array.isArray(payload.data)) {
                clients = payload.data;
            } else {
                console.error('Réponse clients invalide:', response);
                this.allClients = [];
                return;
            }

            this.allClients = clients;
            this.populateClientSelect('clientSelect');
            this.populateClientSelect('editClientSelect');
        } catch (error) {
            console.error('Erreur lors du chargement des clients:', error);
            window.showToast?.error('Erreur lors du chargement des clients');
            this.allClients = [];
        }
    }

    /**
     * Remplit un sélecteur de clients (compatible avec les dropdowns personnalisés)
     */
    populateClientSelect(selectId) {
        // Chercher d'abord l'input caché (dropdown personnalisé)
        const hiddenInput = document.getElementById(selectId);
        if (hiddenInput) {
            // C'est un dropdown personnalisé
            const dropdownElement = hiddenInput.closest('[data-controller*="custom-dropdown"]');
            if (dropdownElement) {
                const controller = this.application.getControllerForElementAndIdentifier(dropdownElement, 'custom-dropdown');
                if (controller) {
                    const options = this.allClients.map(client => ({
                        value: client.id,
                        text: client.nom,
                        html: `<svg class="w-4 h-4 text-purple-600 custom-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                               </svg>${client.nom}`
                    }));
                    controller.updateOptions(options);
                    controller.enable();
                    return;
                }
            }
        }

        // Fallback pour les selects classiques
        const select = document.querySelector(`select[name="${selectId}"]`) || document.getElementById(selectId);
        if (!select) return;

        select.innerHTML = '<option value="">Sélectionner un client</option>';
        this.allClients.forEach(client => {
            const option = document.createElement('option');
            option.value = client.id;
            option.textContent = client.nom;
            select.appendChild(option);
        });
    }

    /**
     * Charge les sites pour un client donné (compatible avec les dropdowns personnalisés)
     */
    async loadSitesForClient(clientId, selectId) {
        // Chercher d'abord l'input caché (dropdown personnalisé)
        const hiddenInput = document.getElementById(selectId);
        let dropdownController = null;

        if (hiddenInput) {
            const dropdownElement = hiddenInput.closest('[data-controller*="custom-dropdown"]');
            if (dropdownElement) {
                dropdownController = this.application.getControllerForElementAndIdentifier(dropdownElement, 'custom-dropdown');
            }
        }

        if (!clientId) {
            if (dropdownController) {
                dropdownController.disable();
                dropdownController.updateOptions([]);
                if (dropdownController.hasButtonTextTarget) {
                    dropdownController.buttonTextTarget.textContent = 'Sélectionner un client d\'abord';
                }
            } else {
                // Fallback pour les selects classiques
                const select = document.querySelector(`select[name="${selectId}"]`) || document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">Sélectionner un site</option>';
                    select.disabled = true;
                }
            }
            return;
        }

        try {
            const response = await window.ajax.get(this.sitesUrlValue.replace('__ID__', clientId));

            // Gérer les différents formats de réponse
            const payload = response.data ?? response;
            let sites;

            if (Array.isArray(payload)) {
                sites = payload;
            } else if (Array.isArray(payload['hydra:member'])) {
                sites = payload['hydra:member'];
            } else if (Array.isArray(payload.data)) {
                sites = payload.data;
            } else {
                console.error('Réponse sites invalide:', response);
                return;
            }

            if (dropdownController) {
                // Dropdown personnalisé
                const options = [
                    {
                        value: '',
                        text: 'Aucun site',
                        html: `<svg class="w-4 h-4 text-indigo-600 custom-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                               </svg>Aucun site`
                    },
                    ...sites.map(site => ({
                        value: site.id,
                        text: site.nom,
                        html: `<svg class="w-4 h-4 text-indigo-600 custom-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                               </svg>${site.nom}`
                    }))
                ];
                dropdownController.updateOptions(options);
                dropdownController.enable();
                dropdownController.selectOption('', true); // Sélectionner "Aucun site" par défaut
            } else {
                // Fallback pour les selects classiques
                const select = document.querySelector(`select[name="${selectId}"]`) || document.getElementById(selectId);
                if (select) {
                    select.innerHTML = '<option value="">Sélectionner un site</option>';
                    sites.forEach(site => {
                        const option = document.createElement('option');
                        option.value = site.id;
                        option.textContent = site.nom;
                        select.appendChild(option);
                    });
                    select.disabled = false;
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des sites:', error);
            window.showToast?.error('Erreur lors du chargement des sites');
        }
    }

    /**
     * Gère l'événement de changement de client depuis le controller de mission
     */
    clientChanged(event) {
        const { clientId, siteId } = event.detail;
        this.loadSitesForClient(clientId, 'editSiteSelect').then(() => {
            if (siteId) {
                // Chercher le dropdown Site et sélectionner la valeur
                const siteInput = document.getElementById('editSiteSelect');
                if (siteInput) {
                    const dropdownElement = siteInput.closest('[data-controller*="custom-dropdown"]');
                    if (dropdownElement) {
                        const controller = this.application.getControllerForElementAndIdentifier(dropdownElement, 'custom-dropdown');
                        if (controller) {
                            controller.selectOption(siteId, true);
                        }
                    } else {
                        // Fallback pour les selects classiques
                        siteInput.value = siteId;
                    }
                }
            }
        });
    }

    /**
     * Pré-remplit les dropdowns lors de l'édition d'une mission
     */
    populateEditDropdowns(event) {
        const missionData = event.detail;

        // S'assurer que les clients sont chargés avant de pré-sélectionner
        if (this.allClients.length === 0) {
            // Attendre que les clients soient chargés
            setTimeout(() => {
                this.populateEditDropdowns(event);
            }, 200);
            return;
        }

        // Pré-remplir le dropdown Client
        if (missionData.clientId) {
            const clientInput = document.getElementById('editClientSelect');
            if (clientInput) {
                const dropdownElement = clientInput.closest('[data-controller*="custom-dropdown"]');
                if (dropdownElement) {
                    const controller = this.application.getControllerForElementAndIdentifier(dropdownElement, 'custom-dropdown');
                    if (controller) {
                        // S'assurer que les options sont chargées
                        this.populateClientSelect('editClientSelect');

                        // Attendre un peu que les options soient rendues
                        setTimeout(() => {
                            controller.selectOption(missionData.clientId.toString(), true);

                            // Charger les sites pour ce client
                            this.loadSitesForClient(missionData.clientId, 'editSiteSelect').then(() => {
                                if (missionData.siteId) {
                                    const siteInput = document.getElementById('editSiteSelect');
                                    const siteDropdownElement = siteInput?.closest('[data-controller*="custom-dropdown"]');
                                    if (siteDropdownElement) {
                                        const siteController = this.application.getControllerForElementAndIdentifier(siteDropdownElement, 'custom-dropdown');
                                        if (siteController) {
                                            setTimeout(() => {
                                                siteController.selectOption(missionData.siteId.toString(), true);
                                            }, 100);
                                        }
                                    }
                                }
                            });
                        }, 100);
                    }
                }
            }
        }

        // Pré-remplir le dropdown Niveau
        setTimeout(() => {
            if (missionData.niveau) {
                const niveauInput = document.getElementById('editNiveau');
                if (niveauInput) {
                    const dropdownElement = niveauInput.closest('[data-controller*="custom-dropdown"]');
                    if (dropdownElement) {
                        const controller = this.application.getControllerForElementAndIdentifier(dropdownElement, 'custom-dropdown');
                        if (controller) {
                            controller.selectOption(missionData.niveau.toString(), true);
                        }
                    }
                }
            }
        }, 200);

        // Pré-remplir le dropdown Zone
        setTimeout(() => {
            if (missionData.zone) {
                const zoneInput = document.getElementById('editZone');
                if (zoneInput) {
                    const dropdownElement = zoneInput.closest('[data-controller*="custom-dropdown"]');
                    if (dropdownElement) {
                        const controller = this.application.getControllerForElementAndIdentifier(dropdownElement, 'custom-dropdown');
                        if (controller) {
                            controller.selectOption(missionData.zone, true);
                        }
                    }
                }
            }
        }, 250);
    }

    /**
     * Gère la fermeture du modal depuis le controller de mission
     */
    modalClosed() {
        // Nettoyer les sélections si nécessaire
        this.dispatch('clearSelection', { bubbles: true });
    }
}
