/**
 * Utilitaire réutilisable pour la sélection par drag
 */

import { throttleAnimationFrame } from './throttle.js';
import { elementIntersectsSelection, viewportToPageCoordinates, viewportToContainerCoordinates } from './intersection.js';
import { globalAutoScroller } from './auto-scroll.js';

/**
 * Classe pour gérer la sélection par drag-and-drop
 */
export class DragSelection {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            threshold: 5, // Seuil minimum de mouvement pour déclencher le drag
            itemSelector: '.user-item',
            selectedClass: 'selected',
            selectingClass: 'selecting',
            draggingClass: 'dragging',
            selectionBoxClass: 'drag-selection-box',
            useContainer: false, // true pour coordonnées relatives au conteneur
            autoScroll: true,
            ...options
        };

        this.isDragging = false;
        this.mouseMoved = false;
        this.startX = 0;
        this.startY = 0;
        this.selectionBox = null;
        this.selectedItems = new Set();

        // Callbacks
        this.onSelectionChange = options.onSelectionChange || (() => {});
        this.onItemClick = options.onItemClick || (() => {});

        // Bind methods
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseMoveStart = this.handleMouseMoveStart.bind(this);
        this.handleMouseUpStart = this.handleMouseUpStart.bind(this);
        this.handleMouseMove = throttleAnimationFrame(this.handleMouseMove.bind(this));
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.preventSelection = this.preventSelection.bind(this);

        this.init();
    }

    /**
     * Initialise les événements
     */
    init() {
        this.container.addEventListener('mousedown', this.handleMouseDown);

        // Nettoyage en cas d'événements inattendus
        window.addEventListener('beforeunload', () => this.cleanup());
        document.addEventListener('mouseleave', (e) => {
            if (this.isDragging && !document.body.contains(e.relatedTarget)) {
                this.cleanup();
            }
        });
        window.addEventListener('resize', () => {
            if (this.isDragging) this.cleanup();
        });
        window.addEventListener('blur', () => {
            if (this.isDragging) this.cleanup();
        });
    }

    /**
     * Gère l'événement mousedown
     */
    handleMouseDown(event) {
        // Ignorer les clics sur certains éléments
        if (this.shouldIgnoreEvent(event)) return;

        event.preventDefault();
        event.stopPropagation();

        // Calculer les coordonnées de départ
        if (this.options.useContainer) {
            const coords = viewportToContainerCoordinates(event.clientX, event.clientY, this.container);
            this.startX = coords.x;
            this.startY = coords.y;
        } else {
            const coords = viewportToPageCoordinates(event.clientX, event.clientY);
            this.startX = coords.x;
            this.startY = coords.y;
        }

        this.mouseMoved = false;

        // Ajouter les événements temporaires pour détecter le mouvement
        document.addEventListener('mousemove', this.handleMouseMoveStart);
        document.addEventListener('mouseup', this.handleMouseUpStart);
    }

    /**
     * Détecte le début du drag (avec seuil)
     */
    handleMouseMoveStart(event) {
        let currentX, currentY;

        if (this.options.useContainer) {
            const coords = viewportToContainerCoordinates(event.clientX, event.clientY, this.container);
            currentX = coords.x;
            currentY = coords.y;
        } else {
            const coords = viewportToPageCoordinates(event.clientX, event.clientY);
            currentX = coords.x;
            currentY = coords.y;
        }

        const deltaX = Math.abs(currentX - this.startX);
        const deltaY = Math.abs(currentY - this.startY);

        // Si le mouvement dépasse le seuil, commencer le drag
        if (deltaX > this.options.threshold || deltaY > this.options.threshold) {
            this.mouseMoved = true;
            this.startDrag(event);
        }
    }

    /**
     * Gère la fin du mouvement initial
     */
    handleMouseUpStart(event) {
        // Nettoyer les événements temporaires
        document.removeEventListener('mousemove', this.handleMouseMoveStart);
        document.removeEventListener('mouseup', this.handleMouseUpStart);

        // Si la souris n'a pas bougé suffisamment, traiter comme un clic
        if (!this.mouseMoved) {
            this.handleClick(event);
        }
    }

    /**
     * Démarre le drag
     */
    startDrag(event) {
        // Supprimer les événements temporaires
        document.removeEventListener('mousemove', this.handleMouseMoveStart);
        document.removeEventListener('mouseup', this.handleMouseUpStart);

        this.isDragging = true;
        document.body.classList.add(this.options.draggingClass);

        // Créer la boîte de sélection
        this.createSelectionBox();

        // Ajouter les événements de drag
        document.addEventListener('mousemove', this.handleMouseMove);
        document.addEventListener('mouseup', this.handleMouseUp);
        document.addEventListener('selectstart', this.preventSelection);
    }

    /**
     * Crée la boîte de sélection visuelle
     */
    createSelectionBox() {
        this.selectionBox = document.createElement('div');
        this.selectionBox.className = this.options.selectionBoxClass;
        this.selectionBox.style.position = 'absolute';
        this.selectionBox.style.left = this.startX + 'px';
        this.selectionBox.style.top = this.startY + 'px';
        this.selectionBox.style.width = '0px';
        this.selectionBox.style.height = '0px';

        if (this.options.useContainer) {
            this.container.style.position = 'relative';
            this.container.appendChild(this.selectionBox);
        } else {
            document.body.appendChild(this.selectionBox);
        }
    }

    /**
     * Gère le mouvement de la souris pendant le drag
     */
    handleMouseMove(event) {
        if (!this.isDragging || !this.selectionBox) return;

        // Gérer le défilement automatique
        if (this.options.autoScroll) {
            if (this.options.useContainer) {
                globalAutoScroller.startContainerScroll(event, this.container);
            } else {
                globalAutoScroller.startWindowScroll(event);
            }
        }

        this.updateSelectionBox(event);
        this.updateSelection();
    }

    /**
     * Met à jour la boîte de sélection
     */
    updateSelectionBox(event) {
        let currentX, currentY;

        if (this.options.useContainer) {
            const coords = viewportToContainerCoordinates(event.clientX, event.clientY, this.container);
            currentX = coords.x;
            currentY = coords.y;
        } else {
            const coords = viewportToPageCoordinates(event.clientX, event.clientY);
            currentX = coords.x;
            currentY = coords.y;
        }

        const left = Math.min(this.startX, currentX);
        const top = Math.min(this.startY, currentY);
        const width = Math.abs(currentX - this.startX);
        const height = Math.abs(currentY - this.startY);

        this.selectionBox.style.left = left + 'px';
        this.selectionBox.style.top = top + 'px';
        this.selectionBox.style.width = width + 'px';
        this.selectionBox.style.height = height + 'px';

        this.currentSelection = { left, top, width, height };
    }

    /**
     * Met à jour la sélection des éléments
     */
    updateSelection() {
        const items = this.container.querySelectorAll(this.options.itemSelector);

        items.forEach(item => {
            const isInSelection = elementIntersectsSelection(
                this.currentSelection,
                item,
                this.options.useContainer ? this.container : null
            );

            if (isInSelection) {
                item.classList.add(this.options.selectingClass);
            } else {
                item.classList.remove(this.options.selectingClass);
            }
        });
    }

    /**
     * Gère la fin du drag
     */
    handleMouseUp(event) {
        if (!this.isDragging) return;

        // Finaliser la sélection
        const selectingItems = this.container.querySelectorAll(`.${this.options.selectingClass}`);
        selectingItems.forEach(item => {
            const itemId = this.getItemId(item);
            if (itemId && !this.selectedItems.has(itemId)) {
                this.selectedItems.add(itemId);
                item.classList.add(this.options.selectedClass);
            }
            item.classList.remove(this.options.selectingClass);
        });

        this.cleanup();
        this.onSelectionChange(this.selectedItems);
    }

    /**
     * Gère les clics simples
     */
    handleClick(event) {
        const item = event.target.closest(this.options.itemSelector);
        if (!item || this.shouldIgnoreEvent(event)) return;

        const itemId = this.getItemId(item);
        if (!itemId) return;

        if (this.selectedItems.has(itemId)) {
            this.selectedItems.delete(itemId);
            item.classList.remove(this.options.selectedClass);
        } else {
            this.selectedItems.add(itemId);
            item.classList.add(this.options.selectedClass);
        }

        this.onSelectionChange(this.selectedItems);
        this.onItemClick(item, itemId);
    }

    /**
     * Nettoie l'état du drag
     */
    cleanup() {
        this.isDragging = false;
        this.mouseMoved = false;

        globalAutoScroller.stop();
        document.body.classList.remove(this.options.draggingClass);

        // Supprimer la boîte de sélection
        if (this.selectionBox) {
            try {
                if (this.options.useContainer && this.container.contains(this.selectionBox)) {
                    this.container.removeChild(this.selectionBox);
                } else if (!this.options.useContainer && document.body.contains(this.selectionBox)) {
                    document.body.removeChild(this.selectionBox);
                }
            } catch (e) {
                console.warn('Erreur lors de la suppression de la boîte de sélection:', e);
            }
            this.selectionBox = null;
        }

        // Nettoyer les classes temporaires
        const selectingItems = this.container.querySelectorAll(`.${this.options.selectingClass}`);
        selectingItems.forEach(item => {
            item.classList.remove(this.options.selectingClass);
        });

        // Supprimer tous les événements
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('mouseup', this.handleMouseUp);
        document.removeEventListener('selectstart', this.preventSelection);
        document.removeEventListener('mousemove', this.handleMouseMoveStart);
        document.removeEventListener('mouseup', this.handleMouseUpStart);
    }

    /**
     * Vérifie si l'événement doit être ignoré
     */
    shouldIgnoreEvent(event) {
        return event.target.closest('button') ||
               event.target.closest('a') ||
               event.target.type === 'checkbox' ||
               event.target.closest('.user-checkbox');
    }

    /**
     * Extrait l'ID d'un élément
     */
    getItemId(item) {
        return parseInt(item.dataset.userId) || null;
    }

    /**
     * Empêche la sélection de texte
     */
    preventSelection(event) {
        event.preventDefault();
        return false;
    }

    /**
     * Sélectionne tous les éléments
     */
    selectAll() {
        const items = this.container.querySelectorAll(this.options.itemSelector);
        items.forEach(item => {
            const itemId = this.getItemId(item);
            if (itemId && !this.selectedItems.has(itemId)) {
                this.selectedItems.add(itemId);
                item.classList.add(this.options.selectedClass);
            }
        });
        this.onSelectionChange(this.selectedItems);
    }

    /**
     * Désélectionne tous les éléments
     */
    clearSelection() {
        this.selectedItems.clear();
        const items = this.container.querySelectorAll(this.options.itemSelector);
        items.forEach(item => {
            item.classList.remove(this.options.selectedClass);
        });
        this.onSelectionChange(this.selectedItems);
    }

    /**
     * Détruit l'instance
     */
    destroy() {
        this.cleanup();
        this.container.removeEventListener('mousedown', this.handleMouseDown);
    }
}
