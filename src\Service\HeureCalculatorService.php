<?php

namespace App\Service;

use App\Entity\User;
use App\Entity\Segment;

class HeureCalculatorService
{
    // Majorations des heures supplémentaires
    private const MAJORATION_25 = 1.25;
    private const MAJORATION_50 = 1.50;
    private const SEUIL_MAJORATION_50 = 43; // Au-delà de 43h/semaine

    /**
     * Calcule la durée d'un segment en heures
     */
    public function getDureeHeures(Segment $segment): float
    {
        if (!$segment->getDateHeureDebut() || !$segment->getDateHeureFin()) {
            return 0;
        }

        return ($segment->getDateHeureFin()->getTimestamp() - $segment->getDateHeureDebut()->getTimestamp()) / 3600;
    }

    /**
     * Calcule les heures normales et supplémentaires d'une semaine
     */
    /**
     * @deprecated Cette méthode est obsolète. Utilisez HeureHebdomadaireService à la place.
     */
    public function calculHeuresSemaine($semaineTravail): array
    {
        // Méthode dépréciée - utilisez HeureHebdomadaireService à la place
        throw new \BadMethodCallException('Cette méthode est obsolète. Utilisez HeureHebdomadaireService::calculerHeuresSemaine() à la place.');


    }

    /**
     * Calcule les heures d'un utilisateur sur une période
     */
    public function calculHeuresPeriode(User $user, array $semainesTravail): array
    {
        $totalNormales = 0;
        $totalSupp25 = 0;
        $totalSupp50 = 0;
        $totalHeures = 0;
        $detailSemaines = [];

        foreach ($semainesTravail as $semaine) {
            $calcul = $this->calculHeuresSemaine($semaine);

            $totalNormales += $calcul['heuresNormales'];
            $totalSupp25 += $calcul['heuresSupp25'];
            $totalSupp50 += $calcul['heuresSupp50'];
            $totalHeures += $calcul['totalHeures'];

            $detailSemaines[] = [
                'semaine' => $semaine->getSemaineAnnee(),
                'calcul' => $calcul
            ];
        }

        return [
            'totalNormales' => $totalNormales,
            'totalSupp25' => $totalSupp25,
            'totalSupp50' => $totalSupp50,
            'totalSupplementaires' => $totalSupp25 + $totalSupp50,
            'totalHeures' => $totalHeures,
            'detailSemaines' => $detailSemaines
        ];
    }

    /**
     * Calcule le coût des heures avec majorations
     */
    public function calculCoutHeures(array $calculHeures, float $tauxHoraire): array
    {
        $coutNormal = $calculHeures['totalNormales'] * $tauxHoraire;
        $coutSupp25 = $calculHeures['totalSupp25'] * $tauxHoraire * self::MAJORATION_25;
        $coutSupp50 = $calculHeures['totalSupp50'] * $tauxHoraire * self::MAJORATION_50;
        $coutTotal = $coutNormal + $coutSupp25 + $coutSupp50;

        return [
            'tauxHoraire' => $tauxHoraire,
            'coutNormal' => $coutNormal,
            'coutSupp25' => $coutSupp25,
            'coutSupp50' => $coutSupp50,
            'coutTotal' => $coutTotal,
            'economiesSansHS' => $coutTotal - ($calculHeures['totalHeures'] * $tauxHoraire)
        ];
    }

    /**
     * Calcule les heures par type de segment
     */
    public function calculHeuresParType(array $segments): array
    {
        $heuresParType = [
            Segment::TYPE_VOYAGE => 0,
            Segment::TYPE_INTERVENTION => 0,
            Segment::TYPE_STAND_BY => 0
        ];

        foreach ($segments as $segment) {
            $duree = $this->getDureeHeures($segment);
            $heuresParType[$segment->getType()] += $duree;
        }

        return $heuresParType;
    }

    /**
     * Vérifie si les heures sont dans les plages normales
     */
    public function isPlageNormale(\DateTimeInterface $debut, \DateTimeInterface $fin): bool
    {
        $heureDebut = (int) $debut->format('H');
        $heureFin = (int) $fin->format('H');

        // Plages normales : 09:00-12:00 et 14:00-18:00
        $dansPlageMatinale = ($heureDebut >= 9 && $heureFin <= 12);
        $dansPlageAprem = ($heureDebut >= 14 && $heureFin <= 18);

        return $dansPlageMatinale || $dansPlageAprem;
    }

    /**
     * Calcule les heures hors plages normales
     */
    public function calculHeuresHorsPlages(array $segments): float
    {
        $totalHorsPlages = 0;

        foreach ($segments as $segment) {
            if (!$this->isPlageNormale($segment->getDateHeureDebut(), $segment->getDateHeureFin())) {
                $totalHorsPlages += $this->getDureeHeures($segment);
            }
        }

        return $totalHorsPlages;
    }

    /**
     * Statistiques des heures pour un utilisateur
     */
    public function getStatistiquesHeures(User $user, array $semainesTravail): array
    {
        $calculPeriode = $this->calculHeuresPeriode($user, $semainesTravail);

        $moyenneHebdo = count($semainesTravail) > 0 ?
            $calculPeriode['totalHeures'] / count($semainesTravail) : 0;

        $tauxHS = $calculPeriode['totalHeures'] > 0 ?
            ($calculPeriode['totalSupplementaires'] / $calculPeriode['totalHeures']) * 100 : 0;

        return [
            'moyenneHebdomadaire' => $moyenneHebdo,
            'tauxHeuresSupplementaires' => $tauxHS,
            'horaireContractuel' => $user->getHoraireHebdo(),
            'ecartMoyenContrat' => $moyenneHebdo - $user->getHoraireHebdo(),
            'calculPeriode' => $calculPeriode
        ];
    }

    /**
     * Obtient les constantes de majoration
     */
    public function getMajorations(): array
    {
        return [
            'majoration25' => self::MAJORATION_25,
            'majoration50' => self::MAJORATION_50,
            'seuilMajoration50' => self::SEUIL_MAJORATION_50
        ];
    }
}
