<?php

namespace App\Service;

use App\Entity\Prime;
use App\Entity\User;
use App\Entity\Mission;
use App\Repository\PrimeRepository;
use App\Repository\SegmentRepository;
use Doctrine\ORM\EntityManagerInterface;

class PrimeManagementService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private PrimeRepository $primeRepository,
        private SegmentRepository $segmentRepository,
        private PrimeCalculatorService $primeCalculator,
        private SegmentValidationService $segmentValidationService
    ) {}

    /**
     * Génère les primes pour une mission sur une période
     */
    public function genererPrimesMission(Mission $mission, \DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        $primesGenerees = [];
        
        foreach ($mission->getUsers() as $user) {
            $prime = $this->genererPrimeUtilisateur($user, $mission, $debut, $fin);
            if ($prime) {
                $primesGenerees[] = $prime;
            }
        }

        return $primesGenerees;
    }

    /**
     * Génère une prime pour un utilisateur sur une mission et une période
     */
    public function genererPrimeUtilisateur(User $user, Mission $mission, \DateTimeInterface $debut, \DateTimeInterface $fin): ?Prime
    {
        // Vérifier si une prime existe déjà
        $primeExistante = $this->primeRepository->findExistingPrime($user, $mission, $debut, $fin);
        if ($primeExistante) {
            return $primeExistante;
        }

        // Calculer le montant de la prime
        $montant = $this->primeCalculator->calculPrimesUser([$mission], $debut, $fin)['total'];
        
        if ($montant <= 0) {
            return null;
        }

        // Créer la prime
        $prime = new Prime();
        $prime->setUser($user)
              ->setMission($mission)
              ->setMontantFloat($montant)
              ->setPeriodeDebut($debut)
              ->setPeriodeFin($fin);

        $this->entityManager->persist($prime);
        $this->entityManager->flush();

        return $prime;
    }

    /**
     * Valide une prime par le responsable OSI
     */
    public function validerPrimeParResponsable(Prime $prime, User $validateur): bool
    {
        if (!$validateur->canValidatePrimesResponsable()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour valider des primes');
        }

        // Vérifier que tous les segments de l'utilisateur sur la période sont validés
        if (!$this->segmentValidationService->tousSegmentsUtilisateurPeriodeValides(
            $prime->getUser(), 
            $prime->getPeriodeDebut(), 
            $prime->getPeriodeFin()
        )) {
            throw new \InvalidArgumentException('Tous les segments de l\'utilisateur doivent être validés avant de valider la prime');
        }

        $prime->validerParResponsableOsi($validateur);
        $this->entityManager->flush();

        return true;
    }

    /**
     * Invalide une prime par le responsable OSI
     */
    public function invaliderPrimeParResponsable(Prime $prime, User $validateur): bool
    {
        if (!$validateur->canValidatePrimesResponsable()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour invalider des primes');
        }

        $prime->invaliderParResponsableOsi();
        $this->entityManager->flush();

        return true;
    }

    /**
     * Valide une prime par l'assistante RH
     */
    public function validerPrimeParRh(Prime $prime, User $validateur): bool
    {
        if (!$validateur->canValidatePrimesRh()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour valider des primes RH');
        }

        if (!$prime->isValidationResponsableOsi()) {
            throw new \InvalidArgumentException('La prime doit d\'abord être validée par le responsable OSI');
        }

        $prime->validerParAssistanteRh($validateur);
        $this->entityManager->flush();

        return true;
    }

    /**
     * Invalide une prime par l'assistante RH
     */
    public function invaliderPrimeParRh(Prime $prime, User $validateur): bool
    {
        if (!$validateur->canValidatePrimesRh()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour invalider des primes RH');
        }

        $prime->invaliderParAssistanteRh();
        $this->entityManager->flush();

        return true;
    }

    /**
     * Recalcule une prime existante
     */
    public function recalculerPrime(Prime $prime): Prime
    {
        $nouveauMontant = $this->primeCalculator->calculPrimesUser(
            [$prime->getMission()], 
            $prime->getPeriodeDebut(), 
            $prime->getPeriodeFin()
        )['total'];

        $prime->setMontantFloat($nouveauMontant);
        $prime->setDateModification(new \DateTime());

        $this->entityManager->flush();

        return $prime;
    }

    /**
     * Supprime une prime
     */
    public function supprimerPrime(Prime $prime, User $utilisateur): bool
    {
        if (!$utilisateur->canValidatePrimesResponsable() && !$utilisateur->isSuperAdmin()) {
            throw new \InvalidArgumentException('L\'utilisateur n\'a pas les droits pour supprimer des primes');
        }

        if ($prime->isCompletelyValidated()) {
            throw new \InvalidArgumentException('Impossible de supprimer une prime complètement validée');
        }

        $this->entityManager->remove($prime);
        $this->entityManager->flush();

        return true;
    }

    /**
     * Génère automatiquement les primes pour toutes les missions sur une période
     */
    public function genererPrimesAutomatiques(\DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        $missions = $this->entityManager->getRepository(Mission::class)->findAll();
        $primesGenerees = [];

        foreach ($missions as $mission) {
            $primes = $this->genererPrimesMission($mission, $debut, $fin);
            $primesGenerees = array_merge($primesGenerees, $primes);
        }

        return $primesGenerees;
    }

    /**
     * Obtient les primes accessibles selon le rôle de l'utilisateur
     */
    public function getPrimesAccessibles(User $utilisateur): array
    {
        if ($utilisateur->isSuperAdmin() || $utilisateur->isResponsablePaie()) {
            return $this->primeRepository->findValidatedPrimes();
        }

        if ($utilisateur->isAssistanteRh()) {
            return $this->primeRepository->findPendingRhValidation();
        }

        if ($utilisateur->isResponsableMission()) {
            return $this->primeRepository->findPendingResponsableValidation();
        }

        // Utilisateur normal : seulement ses propres primes
        return $this->primeRepository->findBy(['user' => $utilisateur]);
    }

    /**
     * Statistiques des primes selon le rôle
     */
    public function getStatistiquesPrimes(User $utilisateur): array
    {
        $stats = $this->primeRepository->getStatistiquesByStatut();

        if ($utilisateur->isUser()) {
            // Pour un utilisateur normal, ajouter ses statistiques personnelles
            $primesMontant = $this->primeRepository->findBy(['user' => $utilisateur]);
            $montantTotal = array_sum(array_map(fn($p) => $p->getMontantFloat(), $primesMontant));
            
            $stats['montant_personnel'] = $montantTotal;
            $stats['nombre_primes_personnel'] = count($primesMontant);
        }

        return $stats;
    }
}
