<!DOCTYPE html>
<html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}OSI Manager{% endblock %}</title>
        <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 128 128%22><text y=%221.2em%22 font-size=%2296%22>🌍</text></svg>">

        {% block stylesheets %}
            <script src="https://cdn.tailwindcss.com"></script>
            <script>
                tailwind.config = {
                    theme: {
                        extend: {
                            colors: {
                                primary: {
                                    50: '#eff6ff',
                                    500: '#3b82f6',
                                    600: '#2563eb',
                                    700: '#1d4ed8',
                                }
                            }
                        }
                    }
                }
            </script>
            <!-- SweetAlert2 CSS -->
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
        {% endblock %}
    </head>
    <body class="bg-gray-50">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="{{ path('app_dashboard') }}" class="text-xl font-bold text-gray-900">
                                🌍 OSI Manager
                            </a>
                        </div>
                        <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                            <a href="{{ path('app_dashboard') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Dashboard
                            </a>
                            <a href="{{ path('app_users') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Utilisateurs
                            </a>
                            <a href="{{ path('app_missions') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Missions
                            </a>
                            <a href="{{ path('app_calendrier') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Calendrier
                            </a>
                            <a href="{{ path('app_heures') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Heures
                            </a>
                            <a href="{{ path('app_primes') }}" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                                Primes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Contenu principal -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            {% block body %}{% endblock %}
        </main>

        {% block javascripts %}
            {% block importmap %}{{ importmap('app') }}{% endblock %}
            <!-- SweetAlert2 JS -->
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.js"></script>
            <script>
                // Fonction utilitaire AJAX pour remplacer Axios
                window.ajax = {
                    get: function(url, options = {}) {
                        return this.request('GET', url, null, options);
                    },
                    post: function(url, data, options = {}) {
                        return this.request('POST', url, data, options);
                    },
                    put: function(url, data, options = {}) {
                        return this.request('PUT', url, data, options);
                    },
                    delete: function(url, options = {}) {
                        return this.request('DELETE', url, null, options);
                    },
                    request: function(method, url, data, options = {}) {
                        return new Promise((resolve, reject) => {
                            const xhr = new XMLHttpRequest();
                            xhr.open(method, url);

                            // Headers par défaut
                            xhr.setRequestHeader('Content-Type', 'application/json');
                            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

                            // Headers personnalisés
                            if (options.headers) {
                                Object.keys(options.headers).forEach(key => {
                                    xhr.setRequestHeader(key, options.headers[key]);
                                });
                            }

                            xhr.onload = function() {
                                try {
                                    const response = {
                                        status: xhr.status,
                                        statusText: xhr.statusText,
                                        data: xhr.responseText ? JSON.parse(xhr.responseText) : null
                                    };

                                    if (xhr.status >= 200 && xhr.status < 300) {
                                        resolve(response);
                                    } else {
                                        reject(response);
                                    }
                                } catch (error) {
                                    console.error('Erreur lors du traitement de la réponse AJAX:', error);
                                    reject({
                                        status: xhr.status,
                                        statusText: xhr.statusText,
                                        data: null,
                                        error: error.message
                                    });
                                }
                            };

                            xhr.onerror = function() {
                                console.error('Erreur réseau lors de la requête AJAX');
                                reject({
                                    status: 0,
                                    statusText: 'Network Error',
                                    data: null,
                                    error: 'Network Error'
                                });
                            };

                            xhr.onerror = function() {
                                reject({
                                    status: xhr.status,
                                    statusText: xhr.statusText,
                                    data: null
                                });
                            };

                            // Envoi des données
                            if (data) {
                                xhr.send(JSON.stringify(data));
                            } else {
                                xhr.send();
                            }
                        });
                    }
                };

                // Utilitaires SweetAlert2 Toast
                window.showToast = {
                    success: function(message, duration = 3000) {
                        Swal.fire({
                            toast: true,
                            position: 'top-end',
                            icon: 'success',
                            title: message,
                            showConfirmButton: false,
                            timer: duration,
                            timerProgressBar: true,
                            background: '#f0f9ff',
                            color: '#065f46',
                            iconColor: '#10b981',
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                    },
                    error: function(message, duration = 5000) {
                        Swal.fire({
                            toast: true,
                            position: 'top-end',
                            icon: 'error',
                            title: message,
                            showConfirmButton: false,
                            timer: duration,
                            timerProgressBar: true,
                            background: '#fef2f2',
                            color: '#991b1b',
                            iconColor: '#ef4444',
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                    },
                    info: function(message, duration = 3000) {
                        Swal.fire({
                            toast: true,
                            position: 'top-end',
                            icon: 'info',
                            title: message,
                            showConfirmButton: false,
                            timer: duration,
                            timerProgressBar: true,
                            background: '#eff6ff',
                            color: '#1e40af',
                            iconColor: '#3b82f6',
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                    },
                    warning: function(message, duration = 4000) {
                        Swal.fire({
                            toast: true,
                            position: 'top-end',
                            icon: 'warning',
                            title: message,
                            showConfirmButton: false,
                            timer: duration,
                            timerProgressBar: true,
                            background: '#fffbeb',
                            color: '#92400e',
                            iconColor: '#f59e0b',
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer)
                                toast.addEventListener('mouseleave', Swal.resumeTimer)
                            }
                        });
                    }
                };

                // Utilitaire pour le spinner de chargement
                window.loadingSpinner = {
                    show: function(message = 'Chargement en cours...') {
                        // Supprimer un éventuel spinner existant
                        this.hide();

                        const overlay = document.createElement('div');
                        overlay.id = 'loading-overlay';
                        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                        overlay.innerHTML = `
                            <div class="bg-white rounded-lg p-6 flex flex-col items-center space-y-4 shadow-xl">
                                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                                <p class="text-gray-700 font-medium">${message}</p>
                            </div>
                        `;
                        document.body.appendChild(overlay);
                    },
                    hide: function() {
                        const overlay = document.getElementById('loading-overlay');
                        if (overlay) {
                            overlay.remove();
                        }
                    }
                };

                // Gestionnaire pour nettoyer les ressources lors de la navigation Turbo
                document.addEventListener('turbo:before-visit', function() {
                    // Nettoyer le spinner de chargement
                    window.loadingSpinner?.hide();

                    // Nettoyer le calendrier si il existe
                    if (window.calendarController && window.calendarController.calendar) {
                        console.log('Nettoyage du calendrier avant navigation Turbo');
                        window.calendarController.calendar.destroy();
                        window.calendarController.calendar = null;
                    }

                    // Nettoyer les timers globaux si ils existent
                    if (window.activeTimers) {
                        window.activeTimers.forEach(timer => clearTimeout(timer));
                        window.activeTimers = [];
                    }

                    if (window.activeIntervals) {
                        window.activeIntervals.forEach(interval => clearInterval(interval));
                        window.activeIntervals = [];
                    }
                });

                // Protection globale contre les erreurs JavaScript d'extensions/outils externes
                window.addEventListener('error', function(event) {
                    // Ignorer les erreurs qui viennent d'extensions ou d'outils de développement
                    if (event.error && event.error.message &&
                        (event.error.message.includes('renderAjaxRequests') ||
                         event.error.message.includes('finishAjaxRequest') ||
                         event.error.message.includes('Cannot read properties of null') ||
                         event.error.message.includes('Expected arc flag') ||
                         event.error.message.includes('<path> attribute d'))) {
                        console.warn('Erreur ignorée (probablement d\'une extension ou SVG malformé):', event.error.message);
                        event.preventDefault();
                        return false;
                    }
                });

                // Utilitaire pour les modales de confirmation modernes
                window.showConfirm = function(options = {}) {
                    return new Promise((resolve) => {
                        const {
                            title = 'Confirmation',
                            message = 'Êtes-vous sûr ?',
                            confirmText = 'Confirmer',
                            cancelText = 'Annuler',
                            type = 'warning', // warning, danger, info
                            icon = '⚠️'
                        } = options;

                        // Supprimer une éventuelle modale existante
                        const existingModal = document.getElementById('confirm-modal');
                        if (existingModal) {
                            existingModal.remove();
                        }

                        // Couleurs selon le type
                        const colors = {
                            warning: {
                                bg: 'bg-yellow-50',
                                icon: 'text-yellow-400',
                                button: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
                            },
                            danger: {
                                bg: 'bg-red-50',
                                icon: 'text-red-400',
                                button: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                            },
                            info: {
                                bg: 'bg-blue-50',
                                icon: 'text-blue-400',
                                button: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                            }
                        };

                        const colorScheme = colors[type] || colors.warning;

                        const modal = document.createElement('div');
                        modal.id = 'confirm-modal';
                        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center';
                        modal.innerHTML = `
                            <div class="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">
                                <div class="p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-10 h-10 rounded-full ${colorScheme.bg} flex items-center justify-center">
                                            <span class="text-xl">${icon}</span>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                                            <p class="mt-2 text-sm text-gray-500">${message}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-gray-50 px-6 py-3 flex flex-row-reverse space-x-2 space-x-reverse rounded-b-lg">
                                    <button id="confirm-btn" class="inline-flex justify-center rounded-md border border-transparent ${colorScheme.button} px-4 py-2 text-sm font-medium text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors">
                                        ${confirmText}
                                    </button>
                                    <button id="cancel-btn" class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                                        ${cancelText}
                                    </button>
                                </div>
                            </div>
                        `;

                        document.body.appendChild(modal);

                        // Animation d'entrée
                        requestAnimationFrame(() => {
                            modal.classList.add('opacity-100');
                        });

                        // Gestion des événements
                        const confirmBtn = modal.querySelector('#confirm-btn');
                        const cancelBtn = modal.querySelector('#cancel-btn');

                        const cleanup = () => {
                            modal.remove();
                        };

                        confirmBtn.addEventListener('click', () => {
                            cleanup();
                            resolve(true);
                        });

                        cancelBtn.addEventListener('click', () => {
                            cleanup();
                            resolve(false);
                        });

                        // Fermer avec Escape
                        const handleKeydown = (e) => {
                            if (e.key === 'Escape') {
                                cleanup();
                                resolve(false);
                                document.removeEventListener('keydown', handleKeydown);
                            }
                        };
                        document.addEventListener('keydown', handleKeydown);

                        // Fermer en cliquant à l'extérieur
                        modal.addEventListener('click', (e) => {
                            if (e.target === modal) {
                                cleanup();
                                resolve(false);
                            }
                        });
                    });
                };
            </script>
        {% endblock %}
    </body>
</html>
