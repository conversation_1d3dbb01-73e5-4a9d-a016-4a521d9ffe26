# Contrôleurs Partagés

Ce dossier contient les contrôleurs Stimulus réutilisables dans plusieurs pages de l'application.

## Contrôleurs disponibles

### `csrf_protection_controller.js`
- **Usage** : Protection CSRF automatique
- **Identifiant** : `csrf-protection`
- **Description** : Gère automatiquement les tokens CSRF pour les formulaires

### `custom_dropdown_controller.js`
- **Usage** : Dropdown personnalisé
- **Identifiant** : `custom-dropdown`
- **Description** : Dropdown avec recherche et sélection multiple

### `country_autocomplete_controller.js`
- **Usage** : Autocomplétion pour les pays
- **Identifiant** : `country-autocomplete`
- **Description** : Champ d'autocomplétion pour sélectionner un pays

### `modal_selection_controller.js`
- **Usage** : Sélection modale générique
- **Identifiant** : `modal-selection`
- **Description** : Gestion des modales de sélection d'éléments

## Principe des contrôleurs partagés

Ces contrôleurs sont conçus pour être :
- **Réutilisables** : Utilisables dans plusieurs contextes
- **Configurables** : Paramétrables via des values Stimulus
- **Indépendants** : Sans dépendances spécifiques à une page
