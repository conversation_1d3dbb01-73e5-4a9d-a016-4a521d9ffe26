<?php

namespace App\Controller\Api;

use App\Entity\Client;
use App\Repository\ClientRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/clients', name: 'api_client_')]
class ClientController extends AbstractController
{
    public function __construct(
        private ClientRepository $clientRepository
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $search = $request->query->get('search');
        $actif = $request->query->get('actif');

        if ($search) {
            $clients = $this->clientRepository->findByNom($search);
        } elseif ($actif !== null) {
            $clients = $this->clientRepository->findActifs();
        } else {
            $clients = $this->clientRepository->findAll();
        }

        return $this->json([
            'data' => $clients,
            'total' => count($clients)
        ], Response::HTTP_OK, [], [
            'groups' => ['client:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(Client $client): JsonResponse
    {
        return $this->json($client, Response::HTTP_OK, [], [
            'groups' => ['client:read', 'client:detail']
        ]);
    }

    #[Route('/{id}/sites', name: 'sites', methods: ['GET'])]
    public function sites(Client $client): JsonResponse
    {
        $sites = $client->getSites();

        return $this->json([
            'data' => $sites,
            'total' => count($sites)
        ], Response::HTTP_OK, [], [
            'groups' => ['site:read']
        ]);
    }
}
