import { Controller } from '@hotwired/stimulus';

/**
 * Controller Stimulus pour la gestion globale des utilisateurs
 */
export default class extends Controller {
    static targets = ['editModal', 'editForm'];
    static values = {
        syncUrl: String,
        deleteUrl: String,
        showUrl: String,
        updateUrl: String
    };

    connect() {
        this.setupFormHandler();
    }

    disconnect() {
        // Nettoyer les ressources quand le contrôleur est déconnecté
        if (this.syncTimer) {
            clearTimeout(this.syncTimer);
        }
    }

    /**
     * Configure le gestionnaire de formulaire d'édition
     */
    setupFormHandler() {
        if (this.hasEditFormTarget) {
            this.editFormTarget.addEventListener('submit', (event) => {
                event.preventDefault();
                this.submitEditForm();
            });
        }
    }

    /**
     * Synchronise les utilisateurs LDAP
     */
    async syncLdapUsers() {
        const confirmed = await window.showConfirm?.({
            title: 'Synchronisation LDAP',
            message: 'Voulez-vous synchroniser tous les utilisateurs LDAP ? Cette opération peut prendre plusieurs minutes.',
            confirmText: 'Synchroniser',
            cancelText: 'Annuler',
            type: 'info',
            icon: '🔄'
        });

        if (!confirmed) return;

        // Afficher le spinner de chargement
        window.loadingSpinner?.show('Synchronisation LDAP en cours...');

        try {
            const response = await window.ajax.post(this.syncUrlValue);
            const result = response.data || response;

            if (result.success) {
                window.showToast?.success(result.message);
                // Déclencher un événement pour rafraîchir le tableau
                this.dispatch('syncCompleted', { bubbles: true });
            } else {
                window.showToast?.error('Erreur lors de la synchronisation : ' + result.message);
            }
        } catch (error) {
            console.error('Erreur lors de la synchronisation LDAP :', error);
            const errMsg = error.response?.data?.message || error.message || 'Erreur inconnue';
            window.showToast?.error('Erreur lors de la synchronisation LDAP : ' + errMsg);
        } finally {
            window.loadingSpinner?.hide();
        }
    }

    /**
     * Édite un utilisateur
     */
    async editUser(event) {
        const userId = event.target.dataset.userId;
        if (!userId) return;

        try {
            const response = await window.ajax.get(this.showUrlValue.replace('__ID__', userId));
            const user = response.data || response;

            this.fillEditForm(user);
            this.editModalTarget.classList.remove('hidden');
        } catch (error) {
            console.error('Erreur lors du chargement de l\'utilisateur:', error);
            window.showToast?.error('Erreur lors du chargement des données de l\'utilisateur');
        }
    }

    /**
     * Remplit le formulaire d'édition avec les données utilisateur
     */
    fillEditForm(user) {
        const form = this.editFormTarget;

        // Remplir les champs du formulaire
        const fields = {
            'editUserId': user.id,
            'editNom': user.nom || '',
            'editPrenom': user.prenom || '',
            'editEmail': user.email || '',
            'editTelephone': user.telephone || '',
            'editHoraireHebdo': user.horaireHebdo || 35,
            'editDateEmbauche': user.dateEmbauche ? user.dateEmbauche.split('T')[0] : '',
            'editDateDepart': user.dateDepart ? user.dateDepart.split('T')[0] : '',
            'editSecteur': user.secteur || '',
            'editUsername': user.username || '',
            'editManager': user.manager || '',
            'editTitre': user.titre || '',
            'editMobile': user.mobile || ''
        };

        Object.entries(fields).forEach(([fieldId, value]) => {
            const field = form.querySelector(`#${fieldId}`);
            if (field) {
                field.value = value;
            }
        });

        // Gérer les checkboxes
        const checkboxes = {
            'editForfaitJour': user.forfaitJour || false,
            'editActif': user.actif !== false,
            'editIsManager': user.isManager || false,
            'editVpn': user.vpn || false
        };

        Object.entries(checkboxes).forEach(([checkboxId, checked]) => {
            const checkbox = form.querySelector(`#${checkboxId}`);
            if (checkbox) {
                checkbox.checked = checked;
            }
        });
    }

    /**
     * Ferme le modal d'édition
     */
    closeEditModal() {
        this.editModalTarget.classList.add('hidden');
        this.editFormTarget.reset();
    }

    /**
     * Ferme le modal en cliquant à l'extérieur
     */
    closeEditModalOnBackdrop(event) {
        if (event.target === this.editModalTarget) {
            this.closeEditModal();
        }
    }

    /**
     * Soumet le formulaire d'édition
     */
    async submitEditForm() {

        const formData = new FormData(this.editFormTarget);
        const userId = formData.get('userId');

        const data = {
            nom: formData.get('nom'),
            prenom: formData.get('prenom'),
            email: formData.get('email'),
            telephone: formData.get('telephone') || null,
            horaireHebdo: parseInt(formData.get('horaireHebdo')),
            forfaitJour: formData.get('forfaitJour') === 'on',
            actif: formData.get('actif') === 'on',
            dateEmbauche: formData.get('dateEmbauche') || null,
            dateDepart: formData.get('dateDepart') || null,
            secteur: formData.get('secteur') || null,
            username: formData.get('username') || null,
            manager: formData.get('manager') || null,
            titre: formData.get('titre') || null,
            mobile: formData.get('mobile') || null,
            isManager: formData.get('isManager') === 'on',
            vpn: formData.get('vpn') === 'on'
        };

        // Afficher le spinner de chargement
        window.loadingSpinner?.show('Modification de l\'utilisateur en cours...');

        try {
            const response = await window.ajax.put(this.updateUrlValue.replace('__ID__', userId), data);

            if (response.status === 200) {
                window.showToast?.success('Utilisateur modifié avec succès');
                this.closeEditModal();
                // Déclencher un événement pour rafraîchir le tableau
                this.dispatch('userUpdated', { bubbles: true });
            }
        } catch (error) {
            console.error('Erreur lors de la modification de l\'utilisateur:', error);
            window.showToast?.error('Erreur lors de la modification de l\'utilisateur. Vérifiez les données saisies.');
        } finally {
            window.loadingSpinner?.hide();
        }
    }

    /**
     * Supprime un utilisateur
     */
    async deleteUser(event) {
        const userId = event.target.dataset.userId;
        const userName = event.target.dataset.userName;

        if (!userId) return;

        const confirmed = await window.showConfirm?.({
            title: 'Retirer l\'utilisateur',
            message: `Êtes-vous sûr de vouloir retirer l'utilisateur "${userName || 'cet utilisateur'}" d'OSI ? Cette action est irréversible.`,
            confirmText: 'Retirer',
            cancelText: 'Annuler',
            type: 'danger',
            icon: '👤'
        });

        if (!confirmed) return;

        // Afficher le spinner de chargement
        window.loadingSpinner?.show('Suppression de l\'utilisateur en cours...');

        try {
            const response = await window.ajax.delete(this.deleteUrlValue.replace('__ID__', userId));
            const result = response.data || response;

            if (result.success) {
                window.showToast?.success('Utilisateur retiré d\'OSI avec succès');

                // Supprimer la ligne du tableau sans recharger la page
                const row = document.querySelector(`tr[data-user-id="${userId}"]`);
                if (row) {
                    row.remove();
                }
            } else {
                window.showToast?.error('Erreur : ' + (result.message || 'Erreur inconnue'));
            }
        } catch (error) {
            console.error('Erreur lors du retrait de l\'utilisateur:', error);
            window.showToast?.error('Erreur lors du retrait de l\'utilisateur d\'OSI: ' + (error.message || 'Erreur inconnue'));
        } finally {
            window.loadingSpinner?.hide();
        }
    }
}
