<?php

namespace App\Repository;

use App\Entity\Prime;
use App\Entity\User;
use App\Entity\Mission;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Prime>
 */
class PrimeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Prime::class);
    }

    public function save(Prime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Prime $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les primes d'un utilisateur sur une période
     */
    public function findByUserAndPeriod(User $user, \DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.user = :user')
            ->andWhere('p.periodeDebut <= :fin')
            ->andWhere('p.periodeFin >= :debut')
            ->setParameter('user', $user)
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->orderBy('p.periodeDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les primes d'une mission
     */
    public function findByMission(Mission $mission): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.mission = :mission')
            ->setParameter('mission', $mission)
            ->orderBy('p.periodeDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les primes en attente de validation responsable OSI
     */
    public function findPendingResponsableValidation(): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.validationResponsableOsi = false OR p.validationResponsableOsi IS NULL')
            ->orderBy('p.dateCreation', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les primes en attente de validation assistante RH
     */
    public function findPendingRhValidation(): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.validationResponsableOsi = true')
            ->andWhere('p.validationAssistanteRh = false OR p.validationAssistanteRh IS NULL')
            ->orderBy('p.dateValidationResponsableOsi', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les primes complètement validées
     */
    public function findValidatedPrimes(): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.validationResponsableOsi = true')
            ->andWhere('p.validationAssistanteRh = true')
            ->orderBy('p.dateValidationAssistanteRh', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les primes validées pour la paie sur une période
     */
    public function findValidatedPrimesForPeriod(\DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.validationResponsableOsi = true')
            ->andWhere('p.validationAssistanteRh = true')
            ->andWhere('p.periodeDebut <= :fin')
            ->andWhere('p.periodeFin >= :debut')
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->orderBy('p.user', 'ASC')
            ->addOrderBy('p.periodeDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Calcule le total des primes validées pour un utilisateur sur une période
     */
    public function getTotalValidatedPrimesForUser(User $user, \DateTimeInterface $debut, \DateTimeInterface $fin): float
    {
        $result = $this->createQueryBuilder('p')
            ->select('SUM(p.montant)')
            ->andWhere('p.user = :user')
            ->andWhere('p.validationResponsableOsi = true')
            ->andWhere('p.validationAssistanteRh = true')
            ->andWhere('p.periodeDebut <= :fin')
            ->andWhere('p.periodeFin >= :debut')
            ->setParameter('user', $user)
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->getQuery()
            ->getSingleScalarResult();

        return (float) ($result ?? 0);
    }

    /**
     * Vérifie si une prime existe déjà pour un utilisateur et une mission sur une période
     */
    public function findExistingPrime(User $user, Mission $mission, \DateTimeInterface $debut, \DateTimeInterface $fin): ?Prime
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.user = :user')
            ->andWhere('p.mission = :mission')
            ->andWhere('p.periodeDebut = :debut')
            ->andWhere('p.periodeFin = :fin')
            ->setParameter('user', $user)
            ->setParameter('mission', $mission)
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Statistiques des primes par statut
     */
    public function getStatistiquesByStatut(): array
    {
        $qb = $this->createQueryBuilder('p');
        
        $enAttente = $qb->select('COUNT(p.id)')
            ->andWhere('p.validationResponsableOsi = false OR p.validationResponsableOsi IS NULL')
            ->getQuery()
            ->getSingleScalarResult();

        $qb = $this->createQueryBuilder('p');
        $enAttenteRh = $qb->select('COUNT(p.id)')
            ->andWhere('p.validationResponsableOsi = true')
            ->andWhere('p.validationAssistanteRh = false OR p.validationAssistanteRh IS NULL')
            ->getQuery()
            ->getSingleScalarResult();

        $qb = $this->createQueryBuilder('p');
        $validees = $qb->select('COUNT(p.id)')
            ->andWhere('p.validationResponsableOsi = true')
            ->andWhere('p.validationAssistanteRh = true')
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'en_attente_responsable' => (int) $enAttente,
            'en_attente_rh' => (int) $enAttenteRh,
            'validees' => (int) $validees,
            'total' => (int) ($enAttente + $enAttenteRh + $validees)
        ];
    }
}
