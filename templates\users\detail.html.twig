{% extends 'base.html.twig' %}

{% block title %}{{ user.nomComplet }}{% endblock %}

{% block body %}
<div class="container mx-auto px-4 py-8">
    <!-- En-tête -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="sm:flex sm:items-center sm:justify-between">
                <div class="sm:flex sm:items-center">
                    <div class="flex-shrink-0">
                        <div class="h-20 w-20 rounded-full bg-blue-500 flex items-center justify-center">
                            <span class="text-white text-2xl font-medium">{{ user.prenom|first }}{{ user.nom|first }}</span>
                        </div>
                    </div>
                    <div class="mt-4 sm:mt-0 sm:ml-6">
                        <h1 class="text-2xl font-bold text-gray-900">{{ user.nomComplet }}</h1>
                        <p class="text-sm text-gray-500">{{ user.roleDisplay }}</p>
                        <div class="mt-2 flex items-center space-x-4">
                            <span class="text-sm text-gray-600">📧 {{ user.email }}</span>
                            {% if user.telephone %}
                                <span class="text-sm text-gray-600">📞 {{ user.telephone }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="mt-5 sm:mt-0">
                    {% if user.actif %}
                        <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            ✅ Actif
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            ❌ Inactif
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Informations détaillées -->
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <!-- Informations personnelles -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Informations</h3>
                <dl class="space-y-3">
                    {% if user.secteur %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Secteur</dt>
                            <dd class="text-sm text-gray-900">{{ user.secteur }}</dd>
                        </div>
                    {% endif %}
                    {% if user.username %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Nom d'utilisateur</dt>
                            <dd class="text-sm text-gray-900">{{ user.username }}</dd>
                        </div>
                    {% endif %}
                    {% if user.manager %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Manager</dt>
                            <dd class="text-sm text-gray-900">{{ user.manager }}</dd>
                        </div>
                    {% endif %}
                    {% if user.titre %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Titre</dt>
                            <dd class="text-sm text-gray-900">{{ user.titre }}</dd>
                        </div>
                    {% endif %}
                    {% if user.isManager is not null %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Manager</dt>
                            <dd class="text-sm text-gray-900">
                                {% if user.isManager %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        👑 Manager
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        👤 Collaborateur
                                    </span>
                                {% endif %}
                            </dd>
                        </div>
                    {% endif %}
                    {% if user.vpn is not null %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Accès VPN</dt>
                            <dd class="text-sm text-gray-900">
                                {% if user.vpn %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        🔒 Activé
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        🔓 Désactivé
                                    </span>
                                {% endif %}
                            </dd>
                        </div>
                    {% endif %}
                    {% if user.mobile %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Mobile</dt>
                            <dd class="text-sm text-gray-900">📱 {{ user.mobile }}</dd>
                        </div>
                    {% endif %}
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Horaire hebdomadaire</dt>
                        <dd class="text-sm text-gray-900">{{ user.horaireHebdo }}h/semaine</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Type de contrat</dt>
                        <dd class="text-sm text-gray-900">
                            {% if user.forfaitJour %}
                                Forfait jour
                            {% else %}
                                Horaire
                            {% endif %}
                        </dd>
                    </div>
                    {% if user.dateEmbauche %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Date d'embauche</dt>
                            <dd class="text-sm text-gray-900">{{ user.dateEmbauche|date('d/m/Y') }}</dd>
                        </div>
                    {% endif %}
                    {% if user.dateDepart %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Date de départ</dt>
                            <dd class="text-sm text-gray-900">{{ user.dateDepart|date('d/m/Y') }}</dd>
                        </div>
                    {% endif %}
                    {% if user.roles is not empty %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Rôles système</dt>
                            <dd class="text-sm text-gray-900">
                                {% for role in user.roles %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1 mb-1">
                                        {{ role }}
                                    </span>
                                {% endfor %}
                            </dd>
                        </div>
                    {% endif %}
                </dl>
            </div>
        </div>

        <!-- Statistiques des heures -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Statistiques des heures</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Total des heures</dt>
                        <dd class="text-2xl font-bold text-blue-600">{{ statsHeures.totalHeures|number_format(1) }}h</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Nombre de segments</dt>
                        <dd class="text-lg text-gray-900">{{ statsHeures.nombreSegments }}</dd>
                    </div>
                    {% if statsHeures.heuresHebdomadaires is defined and statsHeures.heuresHebdomadaires.nombreSemaines > 0 %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Heures récentes (4 dernières semaines)</dt>
                            <dd class="text-lg text-gray-900">{{ statsHeures.heuresHebdomadaires.totaux.heuresSaisies|number_format(1) }}h sur {{ statsHeures.heuresHebdomadaires.nombreSemaines }} semaine(s)</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Moyenne hebdomadaire</dt>
                            <dd class="text-lg text-gray-900">{{ statsHeures.heuresHebdomadaires.moyenneHebdomadaire|number_format(1) }}h</dd>
                        </div>
                    {% endif %}
                </dl>
            </div>
        </div>

        <!-- Missions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Missions ({{ missions|length }})</h3>
                {% if missions|length > 0 %}
                    <div class="space-y-3">
                        {% for mission in missions|slice(0, 5) %}
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                    <p class="text-xs text-gray-500">{{ mission.pays }}</p>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ mission.niveau }}
                                </span>
                            </div>
                        {% endfor %}
                        {% if missions|length > 5 %}
                            <p class="text-xs text-gray-500">... et {{ missions|length - 5 }} autres</p>
                        {% endif %}
                    </div>
                {% else %}
                    <p class="text-sm text-gray-500">Aucune mission assignée</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Note: Les semaines de travail sont maintenant calculées dynamiquement -->
    <div class="mt-6 bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Heures de travail</h3>
            <div class="text-center py-8">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Heures calculées automatiquement</h3>
                    <p class="mt-1 text-sm text-gray-500">
                        Les heures sont maintenant calculées en temps réel depuis les segments de mission.
                    </p>
                    <div class="mt-6">
                        <a href="{{ path('app_heures') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Voir les heures
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="mt-6 flex justify-between">
        <a href="{{ path('app_users') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            ← Retour à la liste
        </a>
        <div class="space-x-3">
            <a href="{{ path('app_heures') }}?user={{ user.id }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                Voir les heures
            </a>
        </div>
    </div>
</div>
{% endblock %}
