<?php

namespace App\Command;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:init-role-metier',
    description: 'Initialise les rôles métiers pour les utilisateurs existants',
)]
class InitRoleMetierCommand extends Command
{
    public function __construct(
        private UserRepository $userRepository,
        private EntityManagerInterface $entityManager
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Affiche les changements sans les appliquer')
            ->setHelp('Cette commande initialise les rôles métiers pour tous les utilisateurs existants qui n\'en ont pas encore.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryRun = $input->getOption('dry-run');

        $io->title('Initialisation des rôles métiers');

        // Récupérer tous les utilisateurs sans rôle métier
        $users = $this->userRepository->createQueryBuilder('u')
            ->where('u.roleMetier IS NULL OR u.roleMetier = :empty')
            ->setParameter('empty', '')
            ->getQuery()
            ->getResult();

        if (empty($users)) {
            $io->success('Tous les utilisateurs ont déjà un rôle métier défini.');
            return Command::SUCCESS;
        }

        $io->note(sprintf('Trouvé %d utilisateur(s) sans rôle métier.', count($users)));

        $changes = [];
        foreach ($users as $user) {
            $roleMetier = $this->determineRoleMetier($user);
            $changes[] = [
                'user' => $user,
                'ancien_role' => $user->getRoleMetier(),
                'nouveau_role' => $roleMetier
            ];

            if (!$dryRun) {
                $user->setRoleMetier($roleMetier);
            }
        }

        // Afficher les changements
        $io->section('Changements à appliquer :');
        $rows = [];
        foreach ($changes as $change) {
            $rows[] = [
                $change['user']->getNomComplet(),
                $change['user']->getEmail(),
                $change['ancien_role'] ?: 'Aucun',
                $change['nouveau_role']
            ];
        }

        $io->table(
            ['Nom', 'Email', 'Ancien rôle', 'Nouveau rôle'],
            $rows
        );

        if ($dryRun) {
            $io->note('Mode dry-run activé. Aucun changement n\'a été appliqué.');
            $io->info('Exécutez la commande sans --dry-run pour appliquer les changements.');
        } else {
            $this->entityManager->flush();
            $io->success(sprintf('%d utilisateur(s) mis à jour avec succès.', count($changes)));
        }

        return Command::SUCCESS;
    }

    private function determineRoleMetier(User $user): string
    {
        // Logique pour déterminer le rôle métier basé sur les rôles Symfony existants
        $roles = $user->getRoles();

        // Super admin
        if (in_array('ROLE_ADMIN', $roles) || in_array('ROLE_SUPER_ADMIN', $roles)) {
            return User::ROLE_METIER_SUPER_ADMIN;
        }

        // Manager = responsable mission par défaut
        if ($user->hasManagerRole()) {
            return User::ROLE_METIER_RESPONSABLE_MISSION;
        }

        // Rôles spécifiques basés sur les noms
        foreach ($roles as $role) {
            if (stripos($role, 'RH') !== false || stripos($role, 'HUMAN') !== false) {
                return User::ROLE_METIER_ASSISTANTE_RH;
            }
            if (stripos($role, 'PAIE') !== false || stripos($role, 'PAYROLL') !== false) {
                return User::ROLE_METIER_RESPONSABLE_PAIE;
            }
        }

        // Basé sur le secteur ou le titre
        $secteur = strtolower($user->getSecteur() ?? '');
        $titre = strtolower($user->getTitre() ?? '');

        if (stripos($secteur, 'rh') !== false || stripos($titre, 'rh') !== false) {
            return User::ROLE_METIER_ASSISTANTE_RH;
        }

        if (stripos($secteur, 'paie') !== false || stripos($titre, 'paie') !== false) {
            return User::ROLE_METIER_RESPONSABLE_PAIE;
        }

        if (stripos($titre, 'responsable') !== false || stripos($titre, 'manager') !== false) {
            return User::ROLE_METIER_RESPONSABLE_MISSION;
        }

        // Par défaut : utilisateur normal
        return User::ROLE_METIER_USER;
    }
}
