{% extends 'base.html.twig' %}

{% block title %}Validation des Segments{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Validation des Segments</h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" id="valider-selection">
                        <i class="fas fa-check"></i> Valider la sélection
                    </button>
                    <a href="{{ path('app_validation_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                    </a>
                </div>
            </div>

            <!-- Statistiques -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.total }}</h4>
                                    <p class="card-text">Total segments</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.valides }}</h4>
                                    <p class="card-text">Validés</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.en_attente }}</h4>
                                    <p class="card-text">En attente</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-hourglass-half fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ statistiques.pourcentage_valides }}%</h4>
                                    <p class="card-text">Taux validation</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-percentage fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Table des segments -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Segments en attente de validation</h5>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all">
                            <label class="form-check-label" for="select-all">
                                Tout sélectionner
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if segments|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="select-all-header">
                                        </th>
                                        <th>Utilisateur</th>
                                        <th>Mission</th>
                                        <th>Type</th>
                                        <th>Début</th>
                                        <th>Fin</th>
                                        <th>Durée</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for segment in segments %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="segment-checkbox" value="{{ segment.id }}">
                                            </td>
                                            <td>{{ segment.user.nomComplet }}</td>
                                            <td>{{ segment.mission.titre }}</td>
                                            <td>
                                                <span class="badge badge-{{ segment.type == 'VOYAGE' ? 'primary' : (segment.type == 'INTERVENTION' ? 'success' : 'info') }}">
                                                    {{ segment.type }}
                                                </span>
                                            </td>
                                            <td>{{ segment.dateHeureDebut|date('d/m/Y H:i') }}</td>
                                            <td>{{ segment.dateHeureFin|date('d/m/Y H:i') }}</td>
                                            <td>{{ (segment.dureeMinutes / 60)|round(2) }}h</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-success btn-valider" data-id="{{ segment.id }}">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-danger btn-invalider" data-id="{{ segment.id }}">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5>Aucun segment en attente de validation</h5>
                            <p class="text-muted">Tous les segments ont été validés.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion de la sélection multiple
    const selectAll = document.getElementById('select-all');
    const selectAllHeader = document.getElementById('select-all-header');
    const checkboxes = document.querySelectorAll('.segment-checkbox');
    const validerSelection = document.getElementById('valider-selection');

    [selectAll, selectAllHeader].forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            checkboxes.forEach(cb => cb.checked = this.checked);
            selectAll.checked = selectAllHeader.checked = this.checked;
        });
    });

    // Validation individuelle
    document.querySelectorAll('.btn-valider').forEach(btn => {
        btn.addEventListener('click', function() {
            const segmentId = this.dataset.id;
            validerSegment(segmentId);
        });
    });

    // Invalidation individuelle
    document.querySelectorAll('.btn-invalider').forEach(btn => {
        btn.addEventListener('click', function() {
            const segmentId = this.dataset.id;
            invaliderSegment(segmentId);
        });
    });

    // Validation en lot
    validerSelection.addEventListener('click', function() {
        const selectedIds = Array.from(document.querySelectorAll('.segment-checkbox:checked'))
            .map(cb => cb.value);
        
        if (selectedIds.length === 0) {
            alert('Veuillez sélectionner au moins un segment');
            return;
        }

        validerSegmentsLot(selectedIds);
    });

    function validerSegment(id) {
        fetch(`{{ path('app_validation_segment_valider', {id: '__ID__'}) }}`.replace('__ID__', id), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }

    function invaliderSegment(id) {
        if (!confirm('Êtes-vous sûr de vouloir invalider ce segment ?')) {
            return;
        }

        fetch(`{{ path('app_validation_segment_invalider', {id: '__ID__'}) }}`.replace('__ID__', id), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }

    function validerSegmentsLot(ids) {
        const formData = new FormData();
        ids.forEach(id => formData.append('segment_ids[]', id));

        fetch('{{ path('app_validation_segments_lot') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Erreur: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue');
        });
    }
});
</script>
{% endblock %}
