{% extends 'base.html.twig' %}

{% block title %}Missions - OSI Manager{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
/* Styles pour le drag-to-select dans les modales de missions */
.user-item {
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.user-item:hover {
    background-color: #f3f4f6;
}

.user-item.selected {
    background-color: #dbeafe !important;
    border-left: 4px solid #3b82f6;
}

.user-item.selecting {
    background-color: #e0e7ff !important;
    border-left: 4px solid #6366f1;
}

.drag-selection-box {
    border: 2px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    pointer-events: none;
    z-index: 1000;
}

.dragging {
    cursor: crosshair;
}

.dragging .user-item {
    pointer-events: none;
}

/* Styles pour les tooltips des utilisateurs */
.tooltip {
    position: absolute;
    z-index: 9999;
    padding: 10px 14px;
    background-color: #1f2937;
    color: white;
    border-radius: 8px;
    font-size: 13px;
    line-height: 1.4;
    max-width: 300px;
    word-wrap: break-word;
    white-space: normal;
    opacity: 0;
    visibility: hidden;
    transform: translateY(5px);
    transition: opacity 0.15s ease, visibility 0.15s ease, transform 0.15s ease;
    pointer-events: none;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -6px;
    border-width: 6px;
    border-style: solid;
    border-color: #1f2937 transparent transparent transparent;
}

/* Tooltip affiché en dessous du bouton */
.tooltip.tooltip-below::after {
    top: -12px;
    border-color: transparent transparent #1f2937 transparent;
}

/* Styles pour les dropdowns personnalisés */
.custom-dropdown {
    position: relative;
}

.custom-dropdown-button {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 12px 16px;
    background-color: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    min-height: 48px;
}

.custom-dropdown-button:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.custom-dropdown-button:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.custom-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    margin-top: 4px;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    max-height: 300px;
    overflow-y: auto;
}

.custom-dropdown-option {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
    transition: all 0.15s ease;
    border: none;
    background: none;
    text-align: left;
}

.custom-dropdown-option:hover {
    background-color: #f3f4f6;
}

.custom-dropdown-option:focus {
    outline: none;
    background-color: #f3f4f6;
}

.custom-dropdown-option.selected,
.custom-dropdown-option.keyboard-focus {
    background-color: #dbeafe;
    color: #1d4ed8;
    font-weight: 500;
}

.custom-dropdown-icon {
    margin-right: 8px;
    flex-shrink: 0;
}

.custom-dropdown-chevron {
    margin-left: 8px;
    transition: transform 0.2s ease;
}

.custom-dropdown-button[aria-expanded="true"] .custom-dropdown-chevron {
    transform: rotate(180deg);
}

/* Styles pour les inputs qui doivent s'aligner avec les dropdowns */
input.custom-dropdown-button {
    cursor: text;
    justify-content: flex-start;
}

input.custom-dropdown-button:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Styles pour les dropdowns désactivés */
.custom-dropdown-button:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
    border-color: #e5e7eb;
}

.custom-dropdown-button:disabled:hover {
    border-color: #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* Styles pour les icônes de pays/zones */
.zone-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.zone-icon.euro {
    background-color: #dbeafe;
    color: #1d4ed8;
}

.zone-icon.hors-euro {
    background-color: #dcfce7;
    color: #166534;
}

.niveau-icon {
    margin-right: 8px;
    color: #f59e0b;
}

.statut-icon {
    margin-right: 8px;
}

.statut-icon.en-cours {
    color: #3b82f6;
}

.statut-icon.prochaines {
    color: #10b981;
}

.statut-icon.terminees {
    color: #6b7280;
}

/* Styles améliorés pour les selects */
select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
    padding-right: 40px !important;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Style spécial pour les selects en lecture seule */
select.bg-gray-50 {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

select.bg-gray-50:focus {
    background-color: #f9fafb;
    border-color: #d1d5db;
    box-shadow: none;
}

/* Amélioration des options dans les selects */
select option {
    padding: 8px 12px;
    background-color: white;
    color: #374151;
}

select option:checked {
    background-color: #3b82f6;
    color: white;
}

select option:hover {
    background-color: #f3f4f6;
}
</style>
{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0"
     data-controller="mission-management mission-modal mission-user-selection"
     data-mission-management-show-url-value="{{ path('api_mission_show', {id: '__ID__'}) }}"
     data-mission-management-update-url-value="{{ path('api_mission_update', {id: '__ID__'}) }}"
     data-mission-management-delete-url-value="{{ path('api_mission_delete', {id: '__ID__'}) }}"
     data-mission-management-create-url-value="{{ path('api_mission_create') }}"
     data-mission-modal-clients-url-value="{{ path('api_client_index') }}"
     data-mission-modal-sites-url-value="{{ path('api_client_sites', {id: '__ID__'}) }}"
     data-mission-user-selection-users-url-value="{{ path('api_user_index') }}"
     data-action="mission-management:clientChanged->mission-modal#clientChanged
                  mission-management:modalClosed->mission-modal#modalClosed
                  mission-management:usersSelected->mission-user-selection#selectUsers
                  mission-management:openEditModal->mission-modal#openEditModal
                  mission-management:closeEditModal->mission-modal#closeEditModal
                  mission-management:populateEditDropdowns->mission-modal#populateEditDropdowns
                  mission-modal:loadUsers->mission-user-selection#loadUsersEvent
                  mission-modal:loadUsersForEdit->mission-user-selection#loadUsersEvent
                  mission-modal:clearSelection->mission-user-selection#clearSelection
                  mission-modal:createMission->mission-management#createMission">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Missions</h1>
            <p class="mt-2 text-gray-600">Gestion des missions et déplacements</p>
        </div>
        {% if app.user.canCreateMission() %}
            <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                <button type="button"
                        data-action="click->mission-modal#openAddModal"
                        class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                    Nouvelle mission
                </button>
            </div>
        {% endif %}
    </div>

    <!-- Filtres -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" action="{{ path('app_missions') }}" class="space-y-4">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
                    <div>
                        <label for="search" class="flex items-center text-sm font-medium text-gray-700 mb-2">
                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Rechercher
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="search"
                                   name="search"
                                   value="{{ filters.search ?? '' }}"
                                   placeholder="Titre ou pays..."
                                   class="custom-dropdown-button w-full pr-10">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                            <svg class="w-4 h-4 mr-2 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Zone
                        </label>
                        <div class="custom-dropdown"
                             data-controller="custom-dropdown"
                             data-custom-dropdown-selected-value="{{ filters.zone ?? '' }}"
                             data-custom-dropdown-name-value="zone">
                            <button type="button"
                                    class="custom-dropdown-button"
                                    data-custom-dropdown-target="button"
                                    data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                    aria-expanded="false">
                                <div class="flex items-center">
                                    <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                        <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </span>
                                    <span data-custom-dropdown-target="buttonText">Toutes les zones</span>
                                </div>
                                <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value=""
                                        data-text="Toutes les zones"
                                        data-action="click->custom-dropdown#selectOption">
                                    <svg class="w-4 h-4 text-teal-600 custom-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Toutes les zones
                                </button>
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value="EURO"
                                        data-text="Zone Euro"
                                        data-action="click->custom-dropdown#selectOption">
                                    <div class="zone-icon euro">🇪🇺</div>
                                    Zone Euro
                                </button>
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value="HORS_EURO"
                                        data-text="Hors Zone Euro"
                                        data-action="click->custom-dropdown#selectOption">
                                    <div class="zone-icon hors-euro">🌍</div>
                                    Hors Zone Euro
                                </button>
                            </div>
                            <input type="hidden" name="zone" data-custom-dropdown-target="hiddenInput" value="{{ filters.zone ?? '' }}">
                        </div>
                    </div>
                    <div>
                        <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                            <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Niveau
                        </label>
                        <div class="custom-dropdown"
                             data-controller="custom-dropdown"
                             data-custom-dropdown-selected-value="{{ filters.niveau ?? '' }}"
                             data-custom-dropdown-name-value="niveau">
                            <button type="button"
                                    class="custom-dropdown-button"
                                    data-custom-dropdown-target="button"
                                    data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                    aria-expanded="false">
                                <div class="flex items-center">
                                    <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    </span>
                                    <span data-custom-dropdown-target="buttonText">Tous les niveaux</span>
                                </div>
                                <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value=""
                                        data-text="Tous les niveaux"
                                        data-action="click->custom-dropdown#selectOption">
                                    <svg class="w-4 h-4 text-purple-600 custom-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    Tous les niveaux
                                </button>
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value="1"
                                        data-text="Niveau 1"
                                        data-action="click->custom-dropdown#selectOption">
                                    <span class="niveau-icon">⭐</span>
                                    Niveau 1
                                </button>
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value="2"
                                        data-text="Niveau 2"
                                        data-action="click->custom-dropdown#selectOption">
                                    <span class="niveau-icon">⭐⭐</span>
                                    Niveau 2
                                </button>
                            </div>
                            <input type="hidden" name="niveau" data-custom-dropdown-target="hiddenInput" value="{{ filters.niveau ?? '' }}">
                        </div>
                    </div>
                    <div>
                        <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                            <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Statut
                        </label>
                        <div class="custom-dropdown"
                             data-controller="custom-dropdown"
                             data-custom-dropdown-selected-value="{{ filters.statut ?? '' }}"
                             data-custom-dropdown-name-value="statut">
                            <button type="button"
                                    class="custom-dropdown-button"
                                    data-custom-dropdown-target="button"
                                    data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                    aria-expanded="false">
                                <div class="flex items-center">
                                    <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </span>
                                    <span data-custom-dropdown-target="buttonText">Tous les statuts</span>
                                </div>
                                <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value=""
                                        data-text="Tous les statuts"
                                        data-action="click->custom-dropdown#selectOption">
                                    <svg class="w-4 h-4 text-green-600 custom-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Tous les statuts
                                </button>
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value="en_cours"
                                        data-text="En cours"
                                        data-action="click->custom-dropdown#selectOption">
                                    <span class="statut-icon en-cours">🔄</span>
                                    En cours
                                </button>
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value="prochaines"
                                        data-text="Prochaines"
                                        data-action="click->custom-dropdown#selectOption">
                                    <span class="statut-icon prochaines">📅</span>
                                    Prochaines
                                </button>
                                <button type="button"
                                        class="custom-dropdown-option"
                                        data-value="terminees"
                                        data-text="Terminées"
                                        data-action="click->custom-dropdown#selectOption">
                                    <span class="statut-icon terminees">✅</span>
                                    Terminées
                                </button>
                            </div>
                            <input type="hidden" name="statut" data-custom-dropdown-target="hiddenInput" value="{{ filters.statut ?? '' }}">
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <div class="flex space-x-3">
                        <button type="submit"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Filtrer
                        </button>
                        <a href="{{ path('app_missions') }}"
                           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Réinitialiser
                        </a>
                    </div>
                    <div class="text-sm text-gray-500">
                        {{ pagination.total_items }} mission{{ pagination.total_items > 1 ? 's' : '' }} trouvée{{ pagination.total_items > 1 ? 's' : '' }}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des missions -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
            {% for mission in missions %}
                <li>
                    <div class="px-4 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">🌍</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="flex items-center">
                                        <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if mission.zone == 'EURO' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                                            {{ mission.zone == 'EURO' ? 'Zone Euro' : 'Hors Zone Euro' }}
                                        </span>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Niveau {{ mission.niveau }}
                                        </span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500 mt-1">
                                        <div class="flex items-center">
                                            {% if mission.users|length > 0 %}
                                                {% set maxVisible = 2 %}
                                                {% set visibleUsers = mission.users|slice(0, maxVisible) %}
                                                {% set remainingUsers = mission.users|slice(maxVisible) %}

                                                <span>
                                                    {% for user in visibleUsers %}
                                                        {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                                    {% endfor %}
                                                </span>

                                                {% if remainingUsers|length > 0 %}
                                                    <span class="relative ml-1">
                                                        <button type="button"
                                                                class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors"
                                                                data-tooltip="true"
                                                                data-tooltip-content="{% for user in remainingUsers %}{{ user.nomComplet }}{% if not loop.last %}, {% endif %}{% endfor %}"
                                                                data-tooltip-placement="top">
                                                            +{{ remainingUsers|length }} autre{{ remainingUsers|length > 1 ? 's' : '' }}
                                                        </button>
                                                    </span>
                                                {% endif %}
                                            {% else %}
                                                <span>Aucun utilisateur assigné</span>
                                            {% endif %}
                                        </div>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.pays }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.dateDebut|date('d/m/Y') }} - {{ mission.dateFin|date('d/m/Y') }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.dureeJours }} jour(s)</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                {% set today = "now"|date("Y-m-d") %}
                                {% set dateDebut = mission.dateDebut|date("Y-m-d") %}
                                {% set dateFin = mission.dateFin|date("Y-m-d") %}

                                {% if dateDebut <= today and dateFin >= today %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        En cours
                                    </span>
                                {% elseif dateDebut > today %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Prochaine
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Terminée
                                    </span>
                                {% endif %}

                                <div class="flex space-x-2">
                                    <a href="{{ path('app_mission_detail', {id: mission.id}) }}" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                        Voir
                                    </a>
                                    <button type="button"
                                            data-action="click->mission-management#editMission"
                                            data-mission-id="{{ mission.id }}"
                                            class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                        Modifier
                                    </button>
                                    <button type="button"
                                            data-action="click->mission-management#deleteMission"
                                            data-mission-id="{{ mission.id }}"
                                            data-mission-title="{{ mission.titre|e('html_attr') }}"
                                            class="text-red-600 hover:text-red-900 text-sm font-medium">
                                        Supprimer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            {% else %}
                <li class="px-4 py-8 text-center text-gray-500">
                    Aucune mission trouvée
                </li>
            {% endfor %}
        </ul>
    </div>

    <!-- Pagination -->
    {% if pagination.total_pages > 1 %}
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow">
            <div class="flex-1 flex justify-between sm:hidden">
                {% if pagination.current_page > 1 %}
                    <a href="{{ path('app_missions', app.request.query.all|merge({page: pagination.current_page - 1})) }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Précédent
                    </a>
                {% endif %}
                {% if pagination.current_page < pagination.total_pages %}
                    <a href="{{ path('app_missions', app.request.query.all|merge({page: pagination.current_page + 1})) }}"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Suivant
                    </a>
                {% endif %}
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Affichage de
                        <span class="font-medium">{{ ((pagination.current_page - 1) * pagination.limit) + 1 }}</span>
                        à
                        <span class="font-medium">{{ min(pagination.current_page * pagination.limit, pagination.total_items) }}</span>
                        sur
                        <span class="font-medium">{{ pagination.total_items }}</span>
                        résultat{{ pagination.total_items > 1 ? 's' : '' }}
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <!-- Bouton Précédent -->
                        {% if pagination.current_page > 1 %}
                            <a href="{{ path('app_missions', app.request.query.all|merge({page: pagination.current_page - 1})) }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Précédent</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% else %}
                            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400">
                                <span class="sr-only">Précédent</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        {% endif %}

                        <!-- Numéros de page -->
                        {% set start_page = max(1, pagination.current_page - 2) %}
                        {% set end_page = min(pagination.total_pages, pagination.current_page + 2) %}

                        {% if start_page > 1 %}
                            <a href="{{ path('app_missions', app.request.query.all|merge({page: 1})) }}"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                1
                            </a>
                            {% if start_page > 2 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% endif %}
                        {% endif %}

                        {% for page in start_page..end_page %}
                            {% if page == pagination.current_page %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ page }}
                                </span>
                            {% else %}
                                <a href="{{ path('app_missions', app.request.query.all|merge({page: page})) }}"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                    {{ page }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if end_page < pagination.total_pages %}
                            {% if end_page < pagination.total_pages - 1 %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                            {% endif %}
                            <a href="{{ path('app_missions', app.request.query.all|merge({page: pagination.total_pages})) }}"
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ pagination.total_pages }}
                            </a>
                        {% endif %}

                        <!-- Bouton Suivant -->
                        {% if pagination.current_page < pagination.total_pages %}
                            <a href="{{ path('app_missions', app.request.query.all|merge({page: pagination.current_page + 1})) }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Suivant</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% else %}
                            <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400">
                                <span class="sr-only">Suivant</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Modal d'ajout de mission -->
<div data-mission-modal-target="addModal"
     data-action="click->mission-modal#closeAddModalOnBackdrop"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-10 mx-auto p-6 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white"
         onclick="event.stopPropagation()">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Nouvelle mission</h3>
                <button type="button"
                        data-action="click->mission-modal#closeAddModal"
                        class="text-gray-400 hover:text-gray-600">
                    <span class="sr-only">Fermer</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <form data-mission-modal-target="addForm">
                <div class="grid grid-cols-2 gap-6">
                    <!-- Colonne gauche : Sélection des utilisateurs -->
                    <div class="space-y-4">
                        <div>
                            <label for="userSelect" class="block text-sm font-medium text-gray-700 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    Utilisateurs *
                                </span>
                            </label>
                            <div class="space-y-3">
                                <input type="text"
                                       data-mission-user-selection-target="createSearch"
                                       placeholder="Rechercher des utilisateurs..."
                                       class="custom-dropdown-button w-full">

                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">💡 Cliquez-glissez pour sélectionner</span>
                                    <div class="flex space-x-2">
                                        <button type="button"
                                                data-action="click->mission-user-selection#selectAllCreate"
                                                class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors">
                                            Tout sélectionner
                                        </button>
                                        <button type="button"
                                                data-action="click->mission-user-selection#clearSelectionCreate"
                                                class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded hover:bg-gray-200 transition-colors">
                                            Tout désélectionner
                                        </button>
                                    </div>
                                </div>

                                <div class="border border-gray-300 rounded-md h-64 overflow-y-auto bg-white user-selection-container">
                                    <div data-mission-user-selection-target="createContainer" class="p-2 relative">
                                        <!-- Les utilisateurs seront chargés ici -->
                                    </div>
                                </div>

                                <div data-mission-user-selection-target="createSelected" class="flex flex-wrap gap-2 max-h-20 overflow-y-auto p-2 bg-gray-50 rounded border">
                                    <!-- Les utilisateurs sélectionnés apparaîtront ici -->
                                </div>
                            </div>
                            <input type="hidden" id="selectedUserIds" name="userIds" required>
                        </div>
                    </div>

                    <!-- Colonne droite : Informations de la mission -->
                    <div class="space-y-6 border-l border-gray-200 pl-6">
                        <div class="mb-4">
                            <h3 class="flex items-center text-sm font-medium text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Informations de la mission
                            </h3>
                        </div>

                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Client *
                            </label>
                            <div class="custom-dropdown"
                                 data-controller="custom-dropdown"
                                 data-custom-dropdown-name-value="clientId"
                                 data-custom-dropdown-required-value="true">
                                <button type="button"
                                        class="custom-dropdown-button"
                                        data-custom-dropdown-target="button"
                                        data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                        aria-expanded="false">
                                    <div class="flex items-center">
                                        <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </span>
                                        <span data-custom-dropdown-target="buttonText">Sélectionner un client</span>
                                    </div>
                                    <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                    <!-- Les options seront chargées dynamiquement -->
                                </div>
                                <input type="hidden" id="clientSelect" name="clientId" data-custom-dropdown-target="hiddenInput" required>
                            </div>
                        </div>

                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Site (optionnel)
                            </label>
                            <div class="custom-dropdown"
                                 data-controller="custom-dropdown"
                                 data-custom-dropdown-name-value="siteId">
                                <button type="button"
                                        class="custom-dropdown-button bg-gray-50"
                                        data-custom-dropdown-target="button"
                                        data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                        aria-expanded="false"
                                        disabled>
                                    <div class="flex items-center">
                                        <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                            <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                        </span>
                                        <span data-custom-dropdown-target="buttonText">Sélectionner un client d'abord</span>
                                    </div>
                                    <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                    <!-- Les options seront chargées dynamiquement -->
                                </div>
                                <input type="hidden" id="siteSelect" name="siteId" data-custom-dropdown-target="hiddenInput">
                            </div>
                        </div>

                        <div>
                            <label for="titreMission" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                <svg class="w-4 h-4 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Titre *
                            </label>
                            <input type="text" id="titreMission" name="titre" required class="custom-dropdown-button w-full">
                        </div>

                        <div data-controller="country-autocomplete"
                             data-country-autocomplete-zone-target-value="#zoneMission"
                             class="relative">
                            <label for="paysMission" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                <svg class="w-4 h-4 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Pays *
                            </label>
                            <input type="text"
                                   id="paysMission"
                                   name="pays"
                                   required
                                   autocomplete="off"
                                   placeholder="Tapez pour rechercher un pays..."
                                   data-country-autocomplete-target="input"
                                   class="custom-dropdown-button w-full">
                            <!-- Le dropdown sera créé automatiquement par le contrôleur -->
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="dateDebutMission" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                    <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Date début
                                </label>
                                <input type="date" id="dateDebutMission" name="dateDebut" required class="custom-dropdown-button w-full">
                            </div>
                            <div>
                                <label for="dateFinMission" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                    <svg class="w-4 h-4 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Date fin
                                </label>
                                <input type="date" id="dateFinMission" name="dateFin" required class="custom-dropdown-button w-full">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                    <svg class="w-4 h-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    Niveau *
                                </label>
                                <div class="custom-dropdown"
                                     data-controller="custom-dropdown"
                                     data-custom-dropdown-name-value="niveau"
                                     data-custom-dropdown-required-value="true">
                                    <button type="button"
                                            class="custom-dropdown-button"
                                            data-custom-dropdown-target="button"
                                            data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                            aria-expanded="false">
                                        <div class="flex items-center">
                                            <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                </svg>
                                            </span>
                                            <span data-custom-dropdown-target="buttonText">Sélectionner un niveau</span>
                                        </div>
                                        <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="1"
                                                data-text="Niveau 1"
                                                data-action="click->custom-dropdown#selectOption">
                                            <span class="niveau-icon">⭐</span>
                                            Niveau 1
                                        </button>
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="2"
                                                data-text="Niveau 2"
                                                data-action="click->custom-dropdown#selectOption">
                                            <span class="niveau-icon">⭐⭐</span>
                                            Niveau 2
                                        </button>
                                    </div>
                                    <input type="hidden" name="niveau" data-custom-dropdown-target="hiddenInput" required>
                                </div>
                            </div>
                            <div>
                                <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                    <svg class="w-4 h-4 mr-2 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Zone *
                                </label>
                                <div class="custom-dropdown"
                                     data-controller="custom-dropdown"
                                     data-custom-dropdown-name-value="zone"
                                     data-custom-dropdown-required-value="true">
                                    <button type="button"
                                            class="custom-dropdown-button"
                                            data-custom-dropdown-target="button"
                                            data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                            aria-expanded="false">
                                        <div class="flex items-center">
                                            <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </span>
                                            <span data-custom-dropdown-target="buttonText">Sélectionner une zone</span>
                                        </div>
                                        <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="EURO"
                                                data-text="Zone Euro"
                                                data-action="click->custom-dropdown#selectOption">
                                            <div class="zone-icon euro">🇪🇺</div>
                                            Zone Euro
                                        </button>
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="HORS_EURO"
                                                data-text="Hors Zone Euro"
                                                data-action="click->custom-dropdown#selectOption">
                                            <div class="zone-icon hors-euro">🌍</div>
                                            Hors Zone Euro
                                        </button>
                                    </div>
                                    <input type="hidden" id="zoneMission" name="zone" data-custom-dropdown-target="hiddenInput" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button"
                            data-action="click->mission-modal#closeAddModal"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Créer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal d'édition de mission -->
<div data-mission-management-target="editModal"
     data-mission-modal-target="editModal"
     data-action="click->mission-modal#closeEditModalOnBackdrop"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-10 mx-auto p-6 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white"
         onclick="event.stopPropagation()">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Modifier la mission</h3>
                <button type="button"
                        data-action="click->mission-modal#closeEditModal"
                        class="text-gray-400 hover:text-gray-600">
                    <span class="sr-only">Fermer</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <form data-mission-management-target="editForm">
                <input type="hidden" id="editMissionId" name="missionId">

                <div class="grid grid-cols-2 gap-6">
                    <!-- Colonne gauche : Sélection des utilisateurs -->
                    <div class="space-y-4">
                        <div>
                            <label for="editUserSelect" class="block text-sm font-medium text-gray-700 mb-3">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    Utilisateurs *
                                </span>
                            </label>
                            <div class="space-y-3">
                                <input type="text"
                                       data-mission-user-selection-target="editSearch"
                                       placeholder="Rechercher des utilisateurs..."
                                       class="custom-dropdown-button w-full">

                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-600">💡 Cliquez-glissez pour sélectionner</span>
                                    <div class="flex space-x-2">
                                        <button type="button"
                                                data-action="click->mission-user-selection#selectAllEdit"
                                                class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors">
                                            Tout sélectionner
                                        </button>
                                        <button type="button"
                                                data-action="click->mission-user-selection#clearSelectionEdit"
                                                class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded hover:bg-gray-200 transition-colors">
                                            Tout désélectionner
                                        </button>
                                    </div>
                                </div>

                                <div class="border border-gray-300 rounded-md h-64 overflow-y-auto bg-white user-selection-container">
                                    <div data-mission-user-selection-target="editContainer" class="p-2 relative">
                                        <!-- Les utilisateurs seront chargés ici -->
                                    </div>
                                </div>

                                <div data-mission-user-selection-target="editSelected" class="flex flex-wrap gap-2 max-h-20 overflow-y-auto p-2 bg-gray-50 rounded border">
                                    <!-- Les utilisateurs sélectionnés apparaîtront ici -->
                                </div>
                            </div>
                            <input type="hidden" id="editSelectedUserIds" name="userIds" required>
                        </div>
                    </div>

                    <!-- Colonne droite : Informations de la mission -->
                    <div class="space-y-6 border-l border-gray-200 pl-6">
                        <div class="mb-4">
                            <h3 class="flex items-center text-sm font-medium text-gray-700">
                                <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Informations de la mission
                            </h3>
                        </div>

                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Client *
                            </label>
                            <div class="custom-dropdown"
                                 data-controller="custom-dropdown"
                                 data-custom-dropdown-name-value="clientId"
                                 data-custom-dropdown-required-value="true">
                                <button type="button"
                                        class="custom-dropdown-button"
                                        data-custom-dropdown-target="button"
                                        data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                        aria-expanded="false">
                                    <div class="flex items-center">
                                        <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </span>
                                        <span data-custom-dropdown-target="buttonText">Sélectionner un client</span>
                                    </div>
                                    <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                    <!-- Les options seront chargées dynamiquement -->
                                </div>
                                <input type="hidden" id="editClientSelect" name="clientId" data-custom-dropdown-target="hiddenInput" required>
                            </div>
                        </div>

                        <div>
                            <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Site (optionnel)
                            </label>
                            <div class="custom-dropdown"
                                 data-controller="custom-dropdown"
                                 data-custom-dropdown-name-value="siteId">
                                <button type="button"
                                        class="custom-dropdown-button bg-gray-50"
                                        data-custom-dropdown-target="button"
                                        data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                        aria-expanded="false"
                                        disabled>
                                    <div class="flex items-center">
                                        <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                            <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 616 0z"></path>
                                            </svg>
                                        </span>
                                        <span data-custom-dropdown-target="buttonText">Sélectionner un client d'abord</span>
                                    </div>
                                    <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                    <!-- Les options seront chargées dynamiquement -->
                                </div>
                                <input type="hidden" id="editSiteSelect" name="siteId" data-custom-dropdown-target="hiddenInput">
                            </div>
                        </div>

                        <div>
                            <label for="editTitre" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                <svg class="w-4 h-4 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                Titre *
                            </label>
                            <input type="text" id="editTitre" name="titre" required class="custom-dropdown-button w-full">
                        </div>

                        <div data-controller="country-autocomplete"
                             data-country-autocomplete-zone-target-value="#editZone"
                             class="relative">
                            <label for="editPays" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                <svg class="w-4 h-4 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Pays *
                            </label>
                            <input type="text"
                                   id="editPays"
                                   name="pays"
                                   required
                                   autocomplete="off"
                                   placeholder="Tapez pour rechercher un pays..."
                                   data-country-autocomplete-target="input"
                                   class="custom-dropdown-button w-full">
                            <!-- Le dropdown sera créé automatiquement par le contrôleur -->
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="editDateDebut" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                    <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Date début
                                </label>
                                <input type="date" id="editDateDebut" name="dateDebut" required class="custom-dropdown-button w-full">
                            </div>
                            <div>
                                <label for="editDateFin" class="flex items-center text-sm font-medium text-gray-700 mb-1">
                                    <svg class="w-4 h-4 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Date fin
                                </label>
                                <input type="date" id="editDateFin" name="dateFin" required class="custom-dropdown-button w-full">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                    <svg class="w-4 h-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    Niveau *
                                </label>
                                <div class="custom-dropdown"
                                     data-controller="custom-dropdown"
                                     data-custom-dropdown-name-value="niveau"
                                     data-custom-dropdown-required-value="true">
                                    <button type="button"
                                            class="custom-dropdown-button"
                                            data-custom-dropdown-target="button"
                                            data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                            aria-expanded="false">
                                        <div class="flex items-center">
                                            <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                                <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                </svg>
                                            </span>
                                            <span data-custom-dropdown-target="buttonText">Sélectionner un niveau</span>
                                        </div>
                                        <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="1"
                                                data-text="Niveau 1"
                                                data-action="click->custom-dropdown#selectOption">
                                            <span class="niveau-icon">⭐</span>
                                            Niveau 1
                                        </button>
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="2"
                                                data-text="Niveau 2"
                                                data-action="click->custom-dropdown#selectOption">
                                            <span class="niveau-icon">⭐⭐</span>
                                            Niveau 2
                                        </button>
                                    </div>
                                    <input type="hidden" id="editNiveau" name="niveau" data-custom-dropdown-target="hiddenInput" required>
                                </div>
                            </div>
                            <div>
                                <label class="flex items-center text-sm font-medium text-gray-700 mb-2">
                                    <svg class="w-4 h-4 mr-2 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Zone *
                                </label>
                                <div class="custom-dropdown"
                                     data-controller="custom-dropdown"
                                     data-custom-dropdown-name-value="zone"
                                     data-custom-dropdown-required-value="true">
                                    <button type="button"
                                            class="custom-dropdown-button"
                                            data-custom-dropdown-target="button"
                                            data-action="click->custom-dropdown#toggle keydown->custom-dropdown#handleKeydown"
                                            aria-expanded="false">
                                        <div class="flex items-center">
                                            <span class="custom-dropdown-icon" data-custom-dropdown-target="buttonIcon">
                                                <svg class="w-4 h-4 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </span>
                                            <span data-custom-dropdown-target="buttonText">Sélectionner une zone</span>
                                        </div>
                                        <svg class="custom-dropdown-chevron w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div class="custom-dropdown-menu hidden" data-custom-dropdown-target="dropdown">
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="EURO"
                                                data-text="Zone Euro"
                                                data-action="click->custom-dropdown#selectOption">
                                            <div class="zone-icon euro">🇪🇺</div>
                                            Zone Euro
                                        </button>
                                        <button type="button"
                                                class="custom-dropdown-option"
                                                data-value="HORS_EURO"
                                                data-text="Hors Zone Euro"
                                                data-action="click->custom-dropdown#selectOption">
                                            <div class="zone-icon hors-euro">🌍</div>
                                            Hors Zone Euro
                                        </button>
                                    </div>
                                    <input type="hidden" id="editZone" name="zone" data-custom-dropdown-target="hiddenInput" required>
                                </div>
                            </div>
                        </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button"
                            data-action="click->mission-modal#closeEditModal"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        Modifier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

</div>

{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
// Fonction pour initialiser les tooltips
function initializeTooltips() {
    // Supprimer les anciens listeners s'ils existent
    const existingTooltips = document.querySelectorAll('.tooltip');
    existingTooltips.forEach(tooltip => tooltip.remove());

    const tooltipButtons = document.querySelectorAll('[data-tooltip="true"]');
    let currentTooltip = null;

    tooltipButtons.forEach(button => {
        // Supprimer les anciens listeners
        button.removeEventListener('mouseenter', button._tooltipMouseEnter);
        button.removeEventListener('mouseleave', button._tooltipMouseLeave);

        // Créer les nouveaux listeners
        button._tooltipMouseEnter = function() {
            // Supprimer le tooltip existant
            if (currentTooltip) {
                currentTooltip.remove();
                currentTooltip = null;
            }

            // Vérifier qu'il y a du contenu à afficher
            if (!this.dataset.tooltipContent || this.dataset.tooltipContent.trim() === '') {
                return;
            }

            // Créer le nouveau tooltip
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.dataset.tooltipContent;

            // Positionner le tooltip
            document.body.appendChild(tooltip);

            const buttonRect = this.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();

            // Prendre en compte le scroll de la page
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

            // Centrer horizontalement par rapport au bouton
            let left = buttonRect.left + scrollLeft + (buttonRect.width / 2) - (tooltipRect.width / 2);

            // Ajuster si le tooltip dépasse à droite
            if (left + tooltipRect.width > window.innerWidth - 20) {
                left = window.innerWidth - tooltipRect.width - 20;
            }

            // Ajuster si le tooltip dépasse à gauche
            if (left < 20) {
                left = 20;
            }

            // Positionner au-dessus du bouton avec le scroll
            let top = buttonRect.top + scrollTop - tooltipRect.height - 12;

            // Si le tooltip sort en haut de l'écran, le positionner en dessous
            if (top < scrollTop + 10) {
                top = buttonRect.bottom + scrollTop + 12;
                // Changer la flèche pour qu'elle pointe vers le haut
                tooltip.classList.add('tooltip-below');
            }

            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';

            // Afficher le tooltip
            setTimeout(() => {
                tooltip.classList.add('show');
            }, 10);

            currentTooltip = tooltip;
        };

        button._tooltipMouseLeave = function() {
            if (currentTooltip) {
                currentTooltip.classList.remove('show');
                setTimeout(() => {
                    if (currentTooltip) {
                        currentTooltip.remove();
                        currentTooltip = null;
                    }
                }, 200);
            }
        };

        // Ajouter les nouveaux listeners
        button.addEventListener('mouseenter', button._tooltipMouseEnter);
        button.addEventListener('mouseleave', button._tooltipMouseLeave);
    });

    // Supprimer le tooltip si on clique ailleurs
    document.addEventListener('click', function() {
        if (currentTooltip) {
            currentTooltip.remove();
            currentTooltip = null;
        }
    });
}

// Initialiser au chargement de la page
document.addEventListener('DOMContentLoaded', initializeTooltips);

// Réinitialiser après chaque rafraîchissement (si nécessaire)
window.addEventListener('load', initializeTooltips);
</script>
{% endblock %}
