# Contrôleurs Heures

Ce dossier contient les contrôleurs Stimulus pour la gestion des heures de travail.

## Contrôleurs disponibles

### `heures_management_controller.js`
- **Usage** : Gestion complète des heures de travail
- **Identifiant** : `heures-management`
- **Page** : `/heures`
- **Description** : Contrôleur principal pour la page de gestion des heures

#### Fonctionnalités
- ✅ Filtrage des données (utilisateur, dates, type contrat)
- ✅ Affichage des statistiques (heures supplémentaires, moyennes)
- ✅ Toggle pour masquer les semaines à 0h
- ✅ Export des données
- ✅ Spinner de chargement
- ✅ Gestion des erreurs
- ✅ Calcul automatique depuis les segments de mission

#### Targets principaux
- `userFilter`, `dateDebut`, `dateFin` : Filtres
- `heuresTableBody` : Tableau des données
- `totalHS`, `moyenneHS`, `semainesHS` : Statistiques
- `loadingSpinner`, `filtrerButton` : État de chargement

#### Actions principales
- `filtrer()` : Applique les filtres
- `resetFilters()` : Remet à zéro les filtres
- `setMoisCourant()` : Filtre sur le mois courant
- `exportData()` : Exporte les données en CSV
- `toggleMasquerSemaines()` : Toggle affichage semaines vides

## Architecture

Le contrôleur suit les bonnes pratiques Stimulus :
- Séparation des responsabilités
- Gestion d'état centralisée
- Feedback utilisateur (spinner, messages d'erreur)
- API REST pour les données
