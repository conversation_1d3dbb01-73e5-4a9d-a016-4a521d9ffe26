<?php

namespace App\Controller;

use Doctrine\DBAL\Connection;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class DatabaseInfoController extends AbstractController
{
    #[Route('/database-info', name: 'database_info')]
    public function databaseInfo(Connection $connection): JsonResponse
    {
        try {
            // Informations sur la base de données
            $databaseName = $connection->getDatabase();
            $platform = $connection->getDatabasePlatform()->getName();
            
            // Liste des tables
            $schemaManager = $connection->createSchemaManager();
            $tables = $schemaManager->listTableNames();
            
            $tableDetails = [];
            foreach ($tables as $tableName) {
                $table = $schemaManager->listTableDetails($tableName);
                $columns = [];
                
                foreach ($table->getColumns() as $column) {
                    $columns[] = [
                        'name' => $column->getName(),
                        'type' => $column->getType()->getName(),
                        'nullable' => !$column->getNotnull(),
                        'default' => $column->getDefault(),
                        'autoIncrement' => $column->getAutoincrement()
                    ];
                }
                
                $tableDetails[$tableName] = [
                    'columns' => $columns,
                    'primaryKey' => $table->getPrimaryKey() ? $table->getPrimaryKey()->getColumns() : [],
                    'foreignKeys' => array_map(function($fk) {
                        return [
                            'name' => $fk->getName(),
                            'localColumns' => $fk->getLocalColumns(),
                            'foreignTable' => $fk->getForeignTableName(),
                            'foreignColumns' => $fk->getForeignColumns()
                        ];
                    }, $table->getForeignKeys())
                ];
            }
            
            // Compter les enregistrements dans chaque table
            $recordCounts = [];
            foreach ($tables as $tableName) {
                try {
                    $count = $connection->fetchOne("SELECT COUNT(*) FROM `$tableName`");
                    $recordCounts[$tableName] = (int) $count;
                } catch (\Exception $e) {
                    $recordCounts[$tableName] = 'Error: ' . $e->getMessage();
                }
            }
            
            return $this->json([
                'database' => [
                    'name' => $databaseName,
                    'platform' => $platform,
                    'connection' => 'MySQL'
                ],
                'tables' => $tables,
                'tableDetails' => $tableDetails,
                'recordCounts' => $recordCounts,
                'totalTables' => count($tables),
                'timestamp' => new \DateTime()
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }
    
    #[Route('/database-test-connection', name: 'database_test_connection')]
    public function testConnection(Connection $connection): JsonResponse
    {
        try {
            // Test de connexion simple
            $result = $connection->fetchOne('SELECT 1 as test');
            
            // Test de version MySQL
            $version = $connection->fetchOne('SELECT VERSION() as version');
            
            // Test des privilèges
            $privileges = $connection->fetchAllAssociative('SHOW GRANTS');
            
            return $this->json([
                'status' => 'success',
                'connection' => 'OK',
                'testQuery' => $result,
                'mysqlVersion' => $version,
                'privileges' => $privileges,
                'timestamp' => new \DateTime()
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ], 500);
        }
    }
}
