<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Suppression complète des tables collaborateur et semaine_travail
 */
final class Version20250721000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Supprime définitivement les tables collaborateur et semaine_travail après migration vers le calcul automatique depuis les segments';
    }

    public function up(Schema $schema): void
    {
        // Supprimer la table semaine_travail si elle existe
        $this->addSql('DROP TABLE IF EXISTS semaine_travail');

        // Supprimer la table collaborateur si elle existe
        $this->addSql('DROP TABLE IF EXISTS collaborateur');
    }

    public function down(Schema $schema): void
    {
        // Recréer la table collaborateur (structure de base)
        $this->addSql('CREATE TABLE collaborateur (
            id INT AUTO_INCREMENT NOT NULL,
            nom VARCHAR(100) NOT NULL,
            prenom VARCHAR(100) NOT NULL,
            email VARCHAR(180) NOT NULL,
            role VARCHAR(50) NOT NULL,
            horaire_hebdo DOUBLE PRECISION NOT NULL,
            forfait_jour TINYINT(1) NOT NULL,
            UNIQUE INDEX UNIQ_collaborateur_email (email),
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Recréer la table semaine_travail (structure de base)
        $this->addSql('CREATE TABLE semaine_travail (
            id INT AUTO_INCREMENT NOT NULL,
            user_id INT NOT NULL,
            semaine_annee VARCHAR(7) NOT NULL,
            heures_saisies DOUBLE PRECISION NOT NULL,
            source VARCHAR(20) DEFAULT \'manuelle\',
            UNIQUE INDEX user_semaine_unique (user_id, semaine_annee),
            INDEX IDX_semaine_travail_user_id (user_id),
            CONSTRAINT FK_semaine_travail_user_id FOREIGN KEY (user_id) REFERENCES user (id),
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
    }
}
