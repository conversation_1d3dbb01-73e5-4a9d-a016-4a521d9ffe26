<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Suppression de la colonne collaborateur_id de la table mission
 */
final class Version20241218000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Supprime la colonne collaborateur_id de la table mission (migration vers le système users)';
    }

    public function up(Schema $schema): void
    {
        // Supprimer la contrainte de clé étrangère d'abord
        $this->addSql('ALTER TABLE mission DROP FOREIGN KEY FK_9067F23CA848E3B1');

        // Supprimer la colonne
        $this->addSql('ALTER TABLE mission DROP COLUMN collaborateur_id');
    }

    public function down(Schema $schema): void
    {
        // Recréer la colonne
        $this->addSql('ALTER TABLE mission ADD collaborateur_id INT DEFAULT NULL');

        // Recréer l'index
        $this->addSql('CREATE INDEX IDX_9067F23CA6B3A0C4 ON mission (collaborateur_id)');

        // Recréer la contrainte de clé étrangère
        $this->addSql('ALTER TABLE mission ADD CONSTRAINT FK_9067F23CA6B3A0C4 FOREIGN KEY (collaborateur_id) REFERENCES collaborateur (id)');
    }
}
