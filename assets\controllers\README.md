# Organisation des Contrôleurs Stimulus

Cette structure organise les contrôleurs Stimulus par fonctionnalité pour une meilleure maintenabilité.

## Structure des dossiers

### `/shared/`
Contrôleurs réutilisables et génériques utilisés dans plusieurs pages :
- `csrf_protection_controller.js` - Protection CSRF
- `custom_dropdown_controller.js` - Dropdown personnalisé
- `country_autocomplete_controller.js` - Autocomplétion pays
- `modal_selection_controller.js` - Sélection modale générique

### `/dashboard/`
Contrôleurs pour la page d'accueil/tableau de bord :
- `main_selection_controller.js` - Sélection principale du dashboard

### `/missions/`
Contrôleurs pour la gestion des missions :
- `mission_management_controller.js` - Gestion des missions
- `mission_modal_controller.js` - Modales de missions
- `mission_user_selection_controller.js` - Sélection d'utilisateurs pour missions

### `/users/`
Contrôleurs pour la gestion des utilisateurs :
- `user_management_controller.js` - Gestion des utilisateurs

### `/heures/`
Contrôleurs pour la gestion des heures :
- `heures_management_controller.js` - Gestion des heures de travail

### `/calendar/`
Contrôleurs pour le calendrier :
- `calendar_controller.js` - Gestion du calendrier

## Conventions de nommage

- Les noms de contrôleurs suivent le pattern `[fonctionnalité]_controller.js`
- Les identifiants Stimulus utilisent des tirets : `data-controller="mission-management"`
- Un contrôleur par fichier
- Documentation JSDoc recommandée pour chaque contrôleur

## Ajout d'un nouveau contrôleur

1. Créer le fichier dans le dossier approprié
2. L'importer dans `bootstrap.js`
3. L'enregistrer avec `app.register()`
4. Mettre à jour cette documentation
