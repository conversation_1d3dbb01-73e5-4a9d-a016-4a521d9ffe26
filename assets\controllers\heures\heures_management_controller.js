import { Controller } from '@hotwired/stimulus';

/**
 * Controller Stimulus pour la gestion des heures
 */
export default class extends Controller {
    static targets = [
        'userFilter', 'dateDebut', 'dateFin', 'typeContratFilter', 'seuilHSFilter',
        'masquerZero', 'toggleSemaines', 'toggleSemainesThumb', 'toggleSemainesLabel',
        'heuresTableBody', 'semainesHS', 'totalHS', 'moyenneHS',
        'addHeuresModal', 'addHeuresForm', 'userHeures', 'semaineAnnee', 'heuresSaisies',
        'heuresValidation', 'heuresPreview', 'previewNormales', 'previewHS25', 'previewHS50', 'previewTotalHS',
        'filtrerButton', 'loadingSpinner', 'filtrerText'
    ];

    static values = {
        apiUrl: String,
        exportUrl: String
    };

    connect() {
        console.log('Contrôleur heures connecté');
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.setupFormHandlers();
        this.initializeToggle();
        this.loadInitialData();
    }

    disconnect() {
        // Nettoyer les ressources si nécessaire
        if (this.loadTimer) {
            clearTimeout(this.loadTimer);
        }
    }

    /**
     * Configure les gestionnaires de formulaires
     */
    setupFormHandlers() {
        if (this.hasAddHeuresFormTarget) {
            this.addHeuresFormTarget.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitHeuresForm();
            });
        }

        // Validation temps réel des heures
        if (this.hasHeuresSaisiesTarget) {
            this.heuresSaisiesTarget.addEventListener('input', (e) => {
                this.validateHeures(e.target);
            });
        }

        // Validation lors du changement d'utilisateur
        if (this.hasUserHeuresTarget) {
            this.userHeuresTarget.addEventListener('change', () => {
                if (this.heuresSaisiesTarget.value) {
                    this.validateHeures(this.heuresSaisiesTarget);
                }
            });
        }
    }

    /**
     * Initialise le toggle pour masquer les semaines à 0h
     */
    initializeToggle() {
        this.setToggleSemaines(true);
    }

    /**
     * Charge les données initiales (mois courant)
     */
    loadInitialData() {
        this.setMoisCourant();
    }

    /**
     * Action : Filtrer les données
     */
    filtrer() {
        this.loadHeuresData(1);
    }

    /**
     * Action : Réinitialiser les filtres
     */
    resetFilters() {
        this.userFilterTarget.value = '';
        this.dateDebutTarget.value = '';
        this.dateFinTarget.value = '';
        if (this.hasTypeContratFilterTarget) this.typeContratFilterTarget.value = '';
        if (this.hasSeuilHSFilterTarget) this.seuilHSFilterTarget.value = '';

        this.setToggleSemaines(true);
        this.loadHeuresData(1);
    }

    /**
     * Action : Définir le mois courant
     */
    setMoisCourant() {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        this.dateDebutTarget.value = firstDay.toISOString().split('T')[0];
        this.dateFinTarget.value = lastDay.toISOString().split('T')[0];

        this.setToggleSemaines(true);
        this.loadHeuresData(1);
    }

    /**
     * Action : Exporter les données
     */
    exportData() {
        const userId = this.userFilterTarget.value;
        const dateDebut = this.dateDebutTarget.value;
        const dateFin = this.dateFinTarget.value;
        const typeContrat = this.hasTypeContratFilterTarget ? this.typeContratFilterTarget.value : '';
        const seuil = this.hasSeuilHSFilterTarget ? this.seuilHSFilterTarget.value : '';

        const params = new URLSearchParams();
        if (userId) params.append('user', userId);
        if (dateDebut) params.append('debut', dateDebut);
        if (dateFin) params.append('fin', dateFin);
        if (typeContrat) params.append('typeContrat', typeContrat);
        if (seuil) params.append('seuil', seuil);
        params.append('export', 'csv');

        const url = this.exportUrlValue + '?' + params.toString();
        window.open(url, '_blank');
    }

    /**
     * Action : Toggle masquer semaines
     */
    toggleMasquerSemaines() {
        const isActive = this.toggleSemainesTarget.getAttribute('aria-checked') === 'true';
        this.setToggleSemaines(!isActive);
        this.loadHeuresData(1);
    }

    /**
     * Affiche l'état de chargement
     */
    showLoading() {
        if (this.hasFiltrerButtonTarget) {
            this.filtrerButtonTarget.disabled = true;
        }
        if (this.hasLoadingSpinnerTarget) {
            this.loadingSpinnerTarget.classList.remove('hidden');
        }
        if (this.hasFiltrerTextTarget) {
            this.filtrerTextTarget.textContent = 'Chargement...';
        }
    }

    /**
     * Masque l'état de chargement
     */
    hideLoading() {
        if (this.hasFiltrerButtonTarget) {
            this.filtrerButtonTarget.disabled = false;
        }
        if (this.hasLoadingSpinnerTarget) {
            this.loadingSpinnerTarget.classList.add('hidden');
        }
        if (this.hasFiltrerTextTarget) {
            this.filtrerTextTarget.textContent = 'Filtrer';
        }
    }

    /**
     * Définit l'état du toggle
     */
    setToggleSemaines(active) {
        if (active) {
            this.toggleSemainesTarget.setAttribute('aria-checked', 'true');
            this.toggleSemainesTarget.classList.remove('bg-gray-200');
            this.toggleSemainesTarget.classList.add('bg-blue-600');
            this.toggleSemainesThumbTarget.classList.remove('translate-x-0');
            this.toggleSemainesThumbTarget.classList.add('translate-x-5');
            this.toggleSemainesLabelTarget.textContent = 'Masquer les semaines à 0h';
            this.masquerZeroTarget.value = 'semaines';
        } else {
            this.toggleSemainesTarget.setAttribute('aria-checked', 'false');
            this.toggleSemainesTarget.classList.remove('bg-blue-600');
            this.toggleSemainesTarget.classList.add('bg-gray-200');
            this.toggleSemainesThumbTarget.classList.remove('translate-x-5');
            this.toggleSemainesThumbTarget.classList.add('translate-x-0');
            this.toggleSemainesLabelTarget.textContent = 'Afficher toutes les semaines';
            this.masquerZeroTarget.value = 'false';
        }
    }

    /**
     * Action : Ouvrir la modal d'ajout
     */
    openAddModal() {
        this.addHeuresModalTarget.classList.remove('hidden');
    }

    /**
     * Action : Fermer une modal
     */
    closeModal(event) {
        const modalId = event.params.modalId;
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    /**
     * Charge les données d'heures depuis l'API
     */
    async loadHeuresData(page = 1) {
        const userId = this.userFilterTarget.value;
        const dateDebut = this.dateDebutTarget.value;
        const dateFin = this.dateFinTarget.value;
        const masquerZero = this.masquerZeroTarget.value;
        const typeContrat = this.hasTypeContratFilterTarget ? this.typeContratFilterTarget.value : '';
        const seuil = this.hasSeuilHSFilterTarget ? this.seuilHSFilterTarget.value : '';

        // Afficher le spinner et réinitialiser les statistiques
        this.showLoading();
        this.updateStatistiques({ totalHS: 0, moyenneHS: 0, semainesAvecHS: 0 });
        this.heuresTableBodyTarget.innerHTML = '<tr><td colspan="8" class="px-6 py-4 text-center text-gray-500">Chargement des données...</td></tr>';

        try {
            let url = this.apiUrlValue;
            const params = new URLSearchParams();

            if (userId) params.append('userId', userId);
            if (dateDebut) params.append('dateDebut', dateDebut);
            if (dateFin) params.append('dateFin', dateFin);
            if (masquerZero) params.append('masquerZero', masquerZero);

            if (params.toString()) {
                url += '?' + params.toString();
            }

            const response = await window.ajax.get(url);

            // Extraire les données de la réponse (window.ajax retourne {status, statusText, data})
            const data = response.data || response;

            // Traiter la réponse selon le format de l'API
            if (Array.isArray(data)) {
                const allSemaines = [];
                data.forEach(userCalc => {
                    if (userCalc.calcul && userCalc.calcul.detailSemaines) {
                        userCalc.calcul.detailSemaines.forEach(semaine => {
                            semaine.user = userCalc.user;
                            allSemaines.push(semaine);
                        });
                    }
                });
                this.displayHeuresData(allSemaines);
            } else if (data && data.detailSemaines) {
                data.detailSemaines.forEach(semaine => {
                    semaine.user = data.user;
                });
                this.displayHeuresData(data.detailSemaines);
            } else {
                console.error('Format de données inattendu:', response);
                this.displayHeuresData([]);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des données:', error);
            this.heuresTableBodyTarget.innerHTML = '<tr><td colspan="8" class="px-6 py-4 text-center text-red-600">Erreur lors du chargement des données</td></tr>';
            window.showToast?.error('Erreur lors du chargement des données');
        } finally {
            // Masquer le spinner dans tous les cas
            this.hideLoading();
        }
    }

    /**
     * Affiche les données d'heures dans le tableau
     */
    displayHeuresData(semaines) {
        this.heuresTableBodyTarget.innerHTML = '';

        // Calculer les statistiques
        let totalHS = 0;
        let semainesAvecHS = 0;
        let totalSemaines = 0;

        if (!semaines || semaines.length === 0) {
            this.heuresTableBodyTarget.innerHTML = '<tr><td colspan="8" class="px-6 py-4 text-center text-gray-500">Aucune donnée disponible</td></tr>';
            this.updateStatistiques({ totalHS: 0, moyenneHS: 0, semainesAvecHS: 0 });
            return;
        }

        semaines.forEach(semaine => {
            if (!semaine || typeof semaine.heuresSaisies !== 'number') {
                console.warn('Données de semaine invalides:', semaine);
                return;
            }

            totalSemaines++;
            const heuresSupp = (semaine.heuresSupp25 || 0) + (semaine.heuresSupp50 || 0);
            totalHS += heuresSupp;

            if (heuresSupp > 0) {
                semainesAvecHS++;
            }

            // Nom de l'utilisateur
            let nomUtilisateur = 'N/A';
            const utilisateur = semaine.user;
            if (utilisateur) {
                if (utilisateur.nomComplet) {
                    nomUtilisateur = utilisateur.nomComplet;
                } else {
                    const nom = utilisateur.nom || '';
                    const prenom = utilisateur.prenom || '';
                    if (nom || prenom) {
                        nomUtilisateur = `${prenom} ${nom}`.trim();
                    }
                }
            }

            const sourceInfo = this.getSourceInfo(semaine.source || 'segments');

            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${nomUtilisateur}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.semaineAnnee || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresSaisies.toFixed(1)}h</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresNormales.toFixed(1)}h</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresSupp25.toFixed(1)}h</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${semaine.heuresSupp50.toFixed(1)}h</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${sourceInfo.class}">
                        ${sourceInfo.icon} ${sourceInfo.text}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <span class="text-gray-400">Calculé auto</span>
                </td>
            `;
            this.heuresTableBodyTarget.appendChild(row);
        });

        const moyenneHS = totalSemaines > 0 ? totalHS / totalSemaines : 0;
        this.updateStatistiques({ totalHS, moyenneHS, semainesAvecHS });
    }

    /**
     * Met à jour les statistiques affichées
     */
    updateStatistiques(stats) {
        this.totalHSTarget.textContent = stats.totalHS.toFixed(1) + 'h';
        this.moyenneHSTarget.textContent = stats.moyenneHS.toFixed(1) + 'h';
        this.semainesHSTarget.textContent = stats.semainesAvecHS;
    }

    /**
     * Retourne les informations de source pour l'affichage
     */
    getSourceInfo(source) {
        switch (source) {
            case 'segments':
                return {
                    icon: '🎯',
                    text: 'Segments',
                    class: 'bg-blue-100 text-blue-800'
                };
            case 'automatique':
                return {
                    icon: '🔄',
                    text: 'Auto',
                    class: 'bg-blue-100 text-blue-800'
                };
            case 'manuelle':
                return {
                    icon: '✏️',
                    text: 'Manuel',
                    class: 'bg-green-100 text-green-800'
                };
            default:
                return {
                    icon: '🎯',
                    text: 'Segments',
                    class: 'bg-blue-100 text-blue-800'
                };
        }
    }

    /**
     * Soumet le formulaire d'ajout d'heures
     */
    async submitHeuresForm() {
        const formData = new FormData(this.addHeuresFormTarget);
        const semaineValue = formData.get('semaineAnnee');
        const semaineFormatted = semaineValue.replace('-W', '-');

        const data = {
            userId: parseInt(formData.get('userId')),
            semaineAnnee: semaineFormatted,
            heuresSaisies: parseFloat(formData.get('heuresSaisies'))
        };

        try {
            // Note: La création manuelle n'est plus supportée
            window.showToast?.warning('La saisie manuelle des heures n\'est plus supportée. Les heures sont calculées automatiquement depuis les segments de mission.');
            this.closeModal({ params: { modalId: 'addHeuresModal' } });
        } catch (error) {
            console.error('Erreur:', error);
            window.showToast?.error('Erreur lors de l\'enregistrement des heures');
        }
    }

    /**
     * Validation temps réel des heures saisies
     */
    validateHeures(input) {
        const heures = parseFloat(input.value) || 0;
        const userId = this.hasUserHeuresTarget ? this.userHeuresTarget.value : '';
        const validationDiv = this.heuresValidationTarget;
        const previewDiv = this.heuresPreviewTarget;

        // Réinitialiser les messages
        validationDiv.innerHTML = '';
        input.classList.remove('border-red-500', 'border-green-500', 'border-orange-500');

        // Validation de base
        if (heures < 0) {
            this.showValidationError(input, validationDiv, 'Les heures ne peuvent pas être négatives');
            previewDiv.classList.add('hidden');
            return;
        }

        if (heures > 168) {
            this.showValidationError(input, validationDiv, 'Il n\'y a que 168 heures dans une semaine');
            previewDiv.classList.add('hidden');
            return;
        }

        // Validation avancée si un utilisateur est sélectionné
        if (userId && heures > 0) {
            const user = this.getUserById(userId);
            if (user) {
                this.validateAndPreviewHeures(input, validationDiv, previewDiv, heures, user);
            }
        } else if (heures > 0) {
            // Aperçu avec horaire standard de 35h
            this.previewHeures(previewDiv, heures, 35);
            input.classList.add('border-green-500');
        } else {
            previewDiv.classList.add('hidden');
        }
    }

    /**
     * Affiche une erreur de validation
     */
    showValidationError(input, validationDiv, message) {
        input.classList.add('border-red-500');
        validationDiv.innerHTML = `<span class="text-red-600">⚠️ ${message}</span>`;
    }

    /**
     * Validation et aperçu avancés
     */
    validateAndPreviewHeures(input, validationDiv, previewDiv, heures, user) {
        const horaireHebdo = user.horaireHebdo || 35;

        // Validation spécifique
        if (heures > 80) {
            this.showValidationError(input, validationDiv, 'Attention : Plus de 80h par semaine peut être problématique');
        } else if (heures > 60) {
            validationDiv.innerHTML = '<span class="text-orange-600">⚠️ Attention : Beaucoup d\'heures supplémentaires</span>';
            input.classList.add('border-orange-500');
        } else {
            input.classList.add('border-green-500');
        }

        // Aperçu du calcul
        this.previewHeures(previewDiv, heures, horaireHebdo);

        // Message spécifique pour forfait jour
        if (user.forfaitJour) {
            const existingMessage = validationDiv.innerHTML;
            validationDiv.innerHTML = existingMessage + '<br><span class="text-blue-600">ℹ️ Utilisateur en forfait jour</span>';
        }
    }

    /**
     * Affiche l'aperçu du calcul des heures
     */
    previewHeures(previewDiv, heures, horaireHebdo) {
        const heuresNormales = Math.min(heures, horaireHebdo);
        const heuresSupp = Math.max(0, heures - horaireHebdo);
        const heuresSupp25 = Math.min(heuresSupp, 8);
        const heuresSupp50 = Math.max(0, heuresSupp - 8);

        this.previewNormalesTarget.textContent = heuresNormales.toFixed(1);
        this.previewHS25Target.textContent = heuresSupp25.toFixed(1);
        this.previewHS50Target.textContent = heuresSupp50.toFixed(1);
        this.previewTotalHSTarget.textContent = (heuresSupp25 + heuresSupp50).toFixed(1);

        previewDiv.classList.remove('hidden');
    }

    /**
     * Récupère les données d'un utilisateur par ID (simplifié)
     */
    getUserById(id) {
        // Pour une vraie application, on récupérerait les données via API
        // Ici on utilise des valeurs par défaut
        return {
            id: id,
            horaireHebdo: 35,
            forfaitJour: false
        };
    }
}
