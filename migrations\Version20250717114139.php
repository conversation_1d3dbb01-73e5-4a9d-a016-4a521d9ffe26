<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250717114139 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Cette migration est obsolète car la table semaine_travail a été supprimée
        // Vérifier si la table existe avant de la modifier
        if ($schema->hasTable('semaine_travail')) {
            $this->addSql('ALTER TABLE semaine_travail ADD source VARCHAR(20) DEFAULT NULL');
        }
    }

    public function down(Schema $schema): void
    {
        // Cette migration est obsolète car la table semaine_travail a été supprimée
        // Vérifier si la table existe avant de la modifier
        if ($schema->hasTable('semaine_travail')) {
            $this->addSql('ALTER TABLE semaine_travail DROP source');
        }
    }
}
