<?php

namespace App\Controller;

use App\Repository\UserRepository;
use App\Repository\MissionRepository;
use App\Repository\SegmentRepository;
use App\Service\PrimeCalculatorService;
use App\Service\HeureCalculatorService;
use App\Service\HeureHebdomadaireService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DashboardController extends AbstractController
{
    public function __construct(
        private UserRepository $userRepository,
        private MissionRepository $missionRepository,
        private SegmentRepository $segmentRepository,
        private PrimeCalculatorService $primeCalculator,
        private HeureCalculatorService $heureCalculator,
        private HeureHebdomadaireService $heureHebdomadaireService
    ) {}

    #[Route('/', name: 'app_dashboard')]
    public function index(): Response
    {
        $user = $this->getUser();
        if (!$user->hasManagerRoleOrAnyRole(['ROLE_USER_INFORMATIQUE'])) {
            return $this->redirectToRoute('app_login');
        }



        // Statistiques générales
        $statsUsers = $this->userRepository->findActifs();
        $statsMissions = $this->missionRepository->getStatistiques();

        // Missions en cours et prochaines
        $missionsEnCours = $this->missionRepository->findEnCours();
        $prochaines = $this->missionRepository->findProchaines(5);

        // Utilisateurs actifs
        $usersActifs = $this->userRepository->findWithMissions();

        // Calculer les statistiques détaillées des utilisateurs
        $statsUsersData = [
            'total' => count($statsUsers),
            'forfaitJour' => count(array_filter($statsUsers, fn($u) => $u->isForfaitJour())),
            'horaire' => count(array_filter($statsUsers, fn($u) => !$u->isForfaitJour())),
            'actifs' => count($statsUsers)
        ];

        if ($user->isUser()) {
            $statsMissions = $this->missionRepository->getStatistiquesForUser($user);
            $missionsEnCours = $this->missionRepository->findEnCoursForUser($user);
            $prochaines = $this->missionRepository->findProchainesForUser($user, 5);
        }

        return $this->render('dashboard/index.html.twig', [
            'statsUsers' => $statsUsersData,
            'statsMissions' => $statsMissions,
            'missionsEnCours' => $missionsEnCours,
            'prochaines' => $prochaines,
            'usersActifs' => $usersActifs,
            // Variables pour compatibilité template
            'statsCollaborateurs' => $statsUsersData,
        ]);
    }

    #[Route('/users', name: 'app_users')]
    public function users(): Response
    {
        $users = $this->userRepository->findActifs();

        return $this->render('users/index.html.twig', [
            'users' => $users,
        ]);
    }

    // Route de compatibilité
    #[Route('/collaborateurs', name: 'app_collaborateurs')]
    public function collaborateurs(): Response
    {
        return $this->redirectToRoute('app_users');
    }

    #[Route('/users/{id}', name: 'app_user_detail')]
    public function userDetail(int $id): Response
    {
        $user = $this->userRepository->find($id);

        if (!$user) {
            throw $this->createNotFoundException('Utilisateur non trouvé');
        }

        $missions = $user->getMissions();
        $segments = $user->getSegments();

        // Calculer les statistiques des heures depuis les segments
        $totalHeures = 0;
        $nombreSegments = $segments->count();

        foreach ($segments as $segment) {
            $totalHeures += $this->heureCalculator->getDureeHeures($segment);
        }

        // Calculer les heures des 4 dernières semaines
        $heuresHebdomadaires = $this->heureHebdomadaireService->calculerHeuresPeriode($user, new \DateTime('-4 weeks'), new \DateTime());

        $statsHeures = [
            'totalHeures' => $totalHeures,
            'nombreSegments' => $nombreSegments,
            'heuresHebdomadaires' => $heuresHebdomadaires,
        ];

        return $this->render('users/detail.html.twig', [
            'user' => $user,
            'missions' => $missions,
            'segments' => $segments,
            'statsHeures' => $statsHeures,
        ]);
    }

    // Route de compatibilité
    // #[Route('/collaborateurs/{id}', name: 'app_collaborateur_detail')]
    // public function collaborateurDetail(int $id): Response
    // {
    //     return $this->redirectToRoute('app_user_detail', ['id' => $id]);
    // }

    #[Route('/missions', name: 'app_missions')]
    public function missions(Request $request): Response
    {
        $user = $this->getUser();
        // Si l'utilisateur a le rôle user, ne retourner que ses missions
        if ($user->isUser()) {
            $criteria = [
                'user' => $user->getId()
            ];
            $result = $this->missionRepository->findByUserAndCriteria($user, $criteria);
            return $this->render('missions/index.html.twig', [
                'missions' => $result,
                'pagination' => null,
                'filters' => []
            ]);
        }
        // Récupérer les paramètres de filtrage et pagination
        $page = max(1, (int) $request->query->get('page', 1));
        $limit = 10; // Nombre de missions par page

        $criteria = [
            'search' => $request->query->get('search'),
            'zone' => $request->query->get('zone'),
            'niveau' => $request->query->get('niveau'),
            'statut' => $request->query->get('statut'),
        ];

        // Récupérer les missions avec pagination et filtres
        $result = $this->missionRepository->findWithCriteriaAndPagination($criteria, $page, $limit);

        return $this->render('missions/index.html.twig', [
            'missions' => $result['missions'],
            'pagination' => [
                'current_page' => $result['page'],
                'total_pages' => $result['totalPages'],
                'total_items' => $result['total'],
                'limit' => $result['limit']
            ],
            'filters' => $criteria
        ]);
    }

    #[Route('/missions/{id}', name: 'app_mission_detail')]
    public function missionDetail(int $id): Response
    {
        $mission = $this->missionRepository->find($id);

        if (!$mission) {
            throw $this->createNotFoundException('Mission non trouvée');
        }

        $segments = $this->segmentRepository->findByMission($mission);
        $primesTotales = $this->primeCalculator->calculPrimesTotalesMission($mission);

        return $this->render('missions/detail.html.twig', [
            'mission' => $mission,
            'segments' => $segments,
            'primesTotales' => $primesTotales,
        ]);
    }

    #[Route('/calendrier', name: 'app_calendrier')]
    public function calendrier(): Response
    {
        return $this->render('calendrier/index.html.twig');
    }

    #[Route('/heures', name: 'app_heures')]
    public function heures(): Response
    {
        $users = $this->userRepository->findActifs();
        // Note: Les semaines avec heures supplémentaires sont maintenant calculées côté client via l'API

        return $this->render('heures/index.html.twig', [
            'users' => $users,
            'semainesAvecHS' => [], // Vide car calculé dynamiquement
        ]);
    }


    #[Route('/primes', name: 'app_primes')]
    public function primes(): Response
    {
        $user = $this->getUser();

        // Vérifier les droits d'accès aux primes selon le rôle métier
        if (!$user->canAccessValidatedPrimes() && !$user->canValidatePrimesResponsable() && !$user->canValidatePrimesRh()) {
            throw $this->createAccessDeniedException('Accès refusé : vous n\'avez pas les droits pour accéder aux primes');
        }

        $missions = $this->missionRepository->findAll();
        $bareme = $this->primeCalculator->getBareme();

        return $this->render('primes/index.html.twig', [
            'missions' => $missions,
            'bareme' => $bareme,
            'user_role' => $user->getRoleMetier(),
        ]);
    }
}
