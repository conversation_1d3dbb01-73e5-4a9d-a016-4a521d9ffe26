import { Controller } from '@hotwired/stimulus';

/**
 * Controller Stimulus pour la gestion des missions (CRUD)
 */
export default class extends Controller {
    static targets = ['editModal', 'editForm'];
    static values = {
        showUrl: String,
        updateUrl: String,
        deleteUrl: String,
        createUrl: String
    };

    connect() {
        this.setupFormHandler();
    }

    /**
     * Configure le gestionnaire de formulaire d'édition
     */
    setupFormHandler() {
        if (this.hasEditFormTarget) {
            this.editFormTarget.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitEditForm();
            });
        }
    }

    /**
     * Édite une mission
     */
    async editMission(event) {
        const missionId = event.target.dataset.missionId;
        if (!missionId) return;

        try {
            const response = await window.ajax.get(this.showUrlValue.replace('__ID__', missionId));
            const mission = response.data || response;

            // Ouvrir d'abord le modal d'édition
            this.dispatch('openEditModal', { bubbles: true });

            // Attendre un peu que le modal soit ouvert et les targets disponibles
            setTimeout(() => {
                this.fillEditForm(mission);
            }, 150);

        } catch (error) {
            console.error('Erreur lors du chargement de la mission:', error);
            window.showToast?.error('Erreur lors du chargement des données de la mission');
        }
    }

    /**
     * Remplit le formulaire d'édition avec les données de la mission
     */
    fillEditForm(mission) {
        // Remplir les champs de base
        document.getElementById('editMissionId').value = mission.id;
        document.getElementById('editTitre').value = mission.titre || '';
        document.getElementById('editPays').value = mission.pays || '';
        document.getElementById('editDateDebut').value = mission.dateDebut ? mission.dateDebut.split('T')[0] : '';
        document.getElementById('editDateFin').value = mission.dateFin ? mission.dateFin.split('T')[0] : '';

        // Préparer les données pour les dropdowns personnalisés
        const missionData = {
            clientId: mission.client?.id,
            siteId: mission.site?.id,
            niveau: mission.niveau,
            zone: mission.zone
        };

        // Pré-remplir les dropdowns personnalisés
        setTimeout(() => {
            this.dispatch('populateEditDropdowns', {
                detail: missionData,
                bubbles: true
            });
        }, 150);

        // Sélectionner les utilisateurs - attendre que les targets soient disponibles
        if (mission.users) {
            setTimeout(() => {
                this.dispatch('usersSelected', {
                    detail: { userIds: mission.users.map(u => u.id) },
                    bubbles: true
                });
            }, 300);
        }
    }



    /**
     * Soumet le formulaire d'édition
     */
    async submitEditForm() {
        const formData = new FormData(this.editFormTarget);
        const missionId = formData.get('missionId');

        // Récupérer les utilisateurs sélectionnés depuis l'événement
        const userIds = this.getSelectedUserIds();
        if (userIds.length === 0) {
            window.showToast?.warning('Veuillez sélectionner au moins un utilisateur');
            return;
        }

        const data = {
            userIds: userIds,
            clientId: parseInt(formData.get('clientId')),
            siteId: formData.get('siteId') ? parseInt(formData.get('siteId')) : null,
            titre: formData.get('titre'),
            pays: formData.get('pays'),
            dateDebut: formData.get('dateDebut'),
            dateFin: formData.get('dateFin'),
            niveau: parseInt(formData.get('niveau')),
            zone: formData.get('zone')
        };

        // Afficher le spinner de chargement
        window.loadingSpinner?.show('Modification de la mission en cours...');

        try {
            const response = await window.ajax.put(this.updateUrlValue.replace('__ID__', missionId), data);

            if (response.status === 200) {
                window.showToast?.success('Mission modifiée avec succès');
                this.dispatch('closeEditModal', { bubbles: true });
                // Rafraîchir la page pour afficher les modifications
                setTimeout(() => {
                    location.reload();
                }, 500);
            }
        } catch (error) {
            console.error('Erreur lors de la modification de la mission:', error);
            window.showToast?.error('Erreur lors de la modification de la mission. Vérifiez les données saisies.');
        } finally {
            window.loadingSpinner?.hide();
        }
    }

    /**
     * Supprime une mission
     */
    async deleteMission(event) {
        const missionId = event.target.dataset.missionId;
        const missionTitle = event.target.dataset.missionTitle;

        if (!missionId) return;

        const confirmed = await window.showConfirm?.({
            title: 'Supprimer la mission',
            message: `Êtes-vous sûr de vouloir supprimer la mission "${missionTitle || 'cette mission'}" ?\n\nCette action est irréversible.`,
            confirmText: 'Supprimer',
            cancelText: 'Annuler',
            type: 'danger',
            icon: '🗑️'
        });

        if (!confirmed) return;

        // Afficher le spinner de chargement
        window.loadingSpinner?.show('Suppression de la mission en cours...');

        try {
            const response = await window.ajax.delete(this.deleteUrlValue.replace('__ID__', missionId));

            if (response.status === 204) {
                window.showToast?.success('Mission supprimée avec succès');
                // Rafraîchir la page pour mettre à jour la liste
                setTimeout(() => {
                    location.reload();
                }, 500);
            }
        } catch (error) {
            console.error('Erreur lors de la suppression de la mission:', error);
            window.showToast?.error('Erreur lors de la suppression de la mission');
        } finally {
            window.loadingSpinner?.hide();
        }
    }

    /**
     * Récupère les IDs des utilisateurs sélectionnés
     */
    getSelectedUserIds() {
        const hiddenInput = document.getElementById('editSelectedUserIds');
        if (hiddenInput && hiddenInput.value) {
            return hiddenInput.value.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        }
        return [];
    }

    /**
     * Gère la création d'une nouvelle mission
     */
    async createMission(event) {
        event.preventDefault();

        const form = event.detail.form;
        const formData = new FormData(form);
        const userIds = this.getCreateSelectedUserIds();

        if (userIds.length === 0) {
            window.showToast?.warning('Veuillez sélectionner au moins un utilisateur');
            return;
        }

        const data = {
            userIds: userIds,
            clientId: parseInt(formData.get('clientId')),
            siteId: formData.get('siteId') ? parseInt(formData.get('siteId')) : null,
            titre: formData.get('titre'),
            pays: formData.get('pays'),
            dateDebut: formData.get('dateDebut'),
            dateFin: formData.get('dateFin'),
            niveau: parseInt(formData.get('niveau')),
            zone: formData.get('zone')
        };

        window.loadingSpinner?.show('Création de la mission en cours...');

        try {
            const response = await window.ajax.post(this.createUrlValue, data);
            if (response.status === 201) {
                window.showToast?.success('Mission créée avec succès');
                this.dispatch('missionCreated', { bubbles: true });
                // Recharger la page pour afficher la nouvelle mission
                location.reload();
            }
        } catch (error) {
            console.error('Erreur lors de la création de la mission:', error);
            window.showToast?.error('Erreur lors de la création de la mission');
        } finally {
            window.loadingSpinner?.hide();
        }
    }

    /**
     * Récupère les IDs des utilisateurs sélectionnés pour la création
     */
    getCreateSelectedUserIds() {
        const hiddenInput = document.getElementById('selectedUserIds');
        if (hiddenInput && hiddenInput.value) {
            return hiddenInput.value.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        }
        return [];
    }
}
