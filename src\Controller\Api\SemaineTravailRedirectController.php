<?php

namespace App\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Contrôleur de redirection pour maintenir la compatibilité avec l'ancienne API SemaineTravail
 * Redirige vers la nouvelle API HeureHebdomadaire
 */
#[Route('/api/semaines-travail', name: 'api_semaines_travail_redirect_')]
class SemaineTravailRedirectController extends AbstractController
{
    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        return $this->json([
            'message' => 'Cette API a été remplacée par /api/heures-hebdomadaires',
            'redirect' => '/api/heures-hebdomadaires',
            'deprecated' => true,
            'migration_guide' => [
                'old' => '/api/semaines-travail',
                'new' => '/api/heures-hebdomadaires',
                'changes' => [
                    'Les données sont maintenant calculées en temps réel depuis les segments',
                    'Plus de synchronisation nécessaire',
                    'Structure de réponse légèrement différente'
                ]
            ]
        ], Response::HTTP_MOVED_PERMANENTLY);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(int $id): JsonResponse
    {
        return $this->json([
            'message' => 'Cette API a été remplacée. Utilisez /api/heures-hebdomadaires/semaine/{userId}/{semaineAnnee}',
            'redirect' => '/api/heures-hebdomadaires',
            'deprecated' => true
        ], Response::HTTP_MOVED_PERMANENTLY);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(): JsonResponse
    {
        return $this->json([
            'message' => 'La création manuelle de semaines de travail n\'est plus nécessaire. Les heures sont calculées automatiquement depuis les segments.',
            'redirect' => '/api/segments pour créer des segments de mission',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT'])]
    public function update(int $id): JsonResponse
    {
        return $this->json([
            'message' => 'La modification manuelle de semaines de travail n\'est plus possible. Modifiez les segments de mission correspondants.',
            'redirect' => '/api/segments pour modifier les segments',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(int $id): JsonResponse
    {
        return $this->json([
            'message' => 'La suppression de semaines de travail n\'est plus nécessaire. Supprimez les segments de mission correspondants.',
            'redirect' => '/api/segments pour supprimer les segments',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }

    #[Route('/heures-supplementaires', name: 'heures_supplementaires', methods: ['GET'])]
    public function heuresSupplementaires(): JsonResponse
    {
        return $this->json([
            'message' => 'Cette API a été remplacée par /api/heures-hebdomadaires/heures-supplementaires',
            'redirect' => '/api/heures-hebdomadaires/heures-supplementaires',
            'deprecated' => true
        ], Response::HTTP_MOVED_PERMANENTLY);
    }

    #[Route('/statistiques', name: 'statistiques', methods: ['GET'])]
    public function statistiques(): JsonResponse
    {
        return $this->json([
            'message' => 'Cette API a été remplacée par /api/heures-hebdomadaires/statistiques',
            'redirect' => '/api/heures-hebdomadaires/statistiques',
            'deprecated' => true
        ], Response::HTTP_MOVED_PERMANENTLY);
    }

    #[Route('/sync/preview/{id}', name: 'sync_preview', methods: ['GET'])]
    public function syncPreview(int $id): JsonResponse
    {
        return $this->json([
            'message' => 'La synchronisation n\'est plus nécessaire. Les heures sont calculées automatiquement depuis les segments.',
            'redirect' => '/api/heures-hebdomadaires pour consulter les heures calculées',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }

    #[Route('/sync/{id}', name: 'sync', methods: ['POST'])]
    public function sync(int $id): JsonResponse
    {
        return $this->json([
            'message' => 'La synchronisation n\'est plus nécessaire. Les heures sont calculées automatiquement depuis les segments.',
            'redirect' => '/api/heures-hebdomadaires pour consulter les heures calculées',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }

    #[Route('/sync/tous', name: 'sync_tous', methods: ['POST'])]
    public function syncTous(): JsonResponse
    {
        return $this->json([
            'message' => 'La synchronisation n\'est plus nécessaire. Les heures sont calculées automatiquement depuis les segments.',
            'redirect' => '/api/heures-hebdomadaires pour consulter les heures calculées',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }

    #[Route('/sync/preview-tous', name: 'sync_preview_tous', methods: ['GET'])]
    public function syncPreviewTous(): JsonResponse
    {
        return $this->json([
            'message' => 'La synchronisation n\'est plus nécessaire. Les heures sont calculées automatiquement depuis les segments.',
            'redirect' => '/api/heures-hebdomadaires pour consulter les heures calculées',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }

    #[Route('/generer-depuis-segments/{id}', name: 'generer_depuis_segments', methods: ['POST'])]
    public function genererDepuisSegments(int $id): JsonResponse
    {
        return $this->json([
            'message' => 'Cette fonctionnalité est maintenant automatique. Les heures sont toujours calculées depuis les segments.',
            'redirect' => '/api/heures-hebdomadaires pour consulter les heures calculées',
            'deprecated' => true
        ], Response::HTTP_GONE);
    }
}
