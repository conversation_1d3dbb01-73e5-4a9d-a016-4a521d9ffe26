<?php

namespace App\Controller\Api;

use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\HeureHebdomadaireService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/heures-hebdomadaires', name: 'api_heures_hebdomadaires_')]
class HeureHebdomadaireController extends AbstractController
{
    public function __construct(
        private HeureHebdomadaireService $heureHebdomadaireService,
        private UserRepository $userRepository
    ) {}

    /**
     * Liste les heures hebdomadaires avec filtres
     */
    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $userId = $request->query->get('userId');
        $dateDebut = $request->query->get('dateDebut');
        $dateFin = $request->query->get('dateFin');
        $semaineAnnee = $request->query->get('semaineAnnee');
        $masquerZero = $request->query->get('masquerZero', 'semaines'); // Par défaut, masquer les semaines à 0

        // Dates par défaut : mois en cours
        if (!$dateDebut && !$dateFin) {
            $dateDebutObj = new \DateTime('first day of this month');
            $dateFinObj = new \DateTime('last day of this month');
        } else {
            $dateDebutObj = $dateDebut ? new \DateTime($dateDebut) : new \DateTime('-3 months');
            $dateFinObj = $dateFin ? new \DateTime($dateFin) : new \DateTime();
        }

        if ($semaineAnnee && $userId) {
            // Calcul pour une semaine spécifique d'un utilisateur
            $user = $this->userRepository->find($userId);
            if (!$user) {
                return $this->json(['error' => 'Utilisateur non trouvé'], Response::HTTP_NOT_FOUND);
            }

            $calcul = $this->heureHebdomadaireService->calculerHeuresSemaine($user, $semaineAnnee);
            return $this->json($calcul);
        }

        if ($userId) {
            // Calcul pour un utilisateur sur une période
            $user = $this->userRepository->find($userId);
            if (!$user) {
                return $this->json(['error' => 'Utilisateur non trouvé'], Response::HTTP_NOT_FOUND);
            }

            $calcul = $this->heureHebdomadaireService->calculerHeuresPeriode($user, $dateDebutObj, $dateFinObj);
            return $this->json($calcul);
        }

        // Calcul pour tous les utilisateurs
        $users = $this->userRepository->findActifs();
        $resultats = [];

        foreach ($users as $user) {
            $calcul = $this->heureHebdomadaireService->calculerHeuresPeriode($user, $dateDebutObj, $dateFinObj);

            // Appliquer le filtrage selon le type demandé
            if ($masquerZero === 'utilisateurs') {
                // Filtrer les utilisateurs avec 0 heures au total
                if ($calcul['totaux']['heuresSaisies'] > 0) {
                    $resultats[] = [
                        'user' => [
                            'id' => $user->getId(),
                            'nomComplet' => $user->getNomComplet(),
                            'horaireHebdo' => $user->getHoraireHebdo()
                        ],
                        'calcul' => $calcul
                    ];
                }
            } elseif ($masquerZero === 'semaines') {
                // Filtrer les semaines avec 0 heures
                $semainesFiltrees = array_filter($calcul['detailSemaines'], function($semaine) {
                    return $semaine['heuresSaisies'] > 0;
                });

                // Recalculer les totaux pour les semaines filtrées
                $totauxFiltres = [
                    'heuresSaisies' => 0,
                    'heuresNormales' => 0,
                    'heuresSupplementaires' => 0,
                    'heuresSupp25' => 0,
                    'heuresSupp50' => 0
                ];

                foreach ($semainesFiltrees as $semaine) {
                    $totauxFiltres['heuresSaisies'] += $semaine['heuresSaisies'];
                    $totauxFiltres['heuresNormales'] += $semaine['heuresNormales'];
                    $totauxFiltres['heuresSupplementaires'] += $semaine['heuresSupplementaires'];
                    $totauxFiltres['heuresSupp25'] += $semaine['heuresSupp25'];
                    $totauxFiltres['heuresSupp50'] += $semaine['heuresSupp50'];
                }

                // N'inclure l'utilisateur que s'il a au moins une semaine avec des heures
                if (!empty($semainesFiltrees)) {
                    $calculFiltre = $calcul;
                    $calculFiltre['detailSemaines'] = array_values($semainesFiltrees);
                    $calculFiltre['totaux'] = $totauxFiltres;
                    $calculFiltre['nombreSemaines'] = count($semainesFiltrees);
                    $calculFiltre['moyenneHebdomadaire'] = count($semainesFiltrees) > 0 ? $totauxFiltres['heuresSaisies'] / count($semainesFiltrees) : 0;

                    $resultats[] = [
                        'user' => [
                            'id' => $user->getId(),
                            'nomComplet' => $user->getNomComplet(),
                            'horaireHebdo' => $user->getHoraireHebdo()
                        ],
                        'calcul' => $calculFiltre
                    ];
                }
            } else {
                // Afficher tout (pas de filtrage)
                $resultats[] = [
                    'user' => [
                        'id' => $user->getId(),
                        'nomComplet' => $user->getNomComplet(),
                        'horaireHebdo' => $user->getHoraireHebdo()
                    ],
                    'calcul' => $calcul
                ];
            }
        }

        return $this->json($resultats);
    }

    /**
     * Détail d'une semaine spécifique
     */
    #[Route('/semaine/{userId}/{semaineAnnee}', name: 'semaine_detail', methods: ['GET'])]
    public function semaineDetail(int $userId, string $semaineAnnee): JsonResponse
    {
        $user = $this->userRepository->find($userId);
        if (!$user) {
            return $this->json(['error' => 'Utilisateur non trouvé'], Response::HTTP_NOT_FOUND);
        }

        $calcul = $this->heureHebdomadaireService->calculerHeuresSemaine($user, $semaineAnnee);

        return $this->json([
            'semaine' => $calcul,
            'segments' => array_map(function($segment) {
                return [
                    'id' => $segment->getId(),
                    'type' => $segment->getType(),
                    'dateHeureDebut' => $segment->getDateHeureDebut()->format('Y-m-d H:i:s'),
                    'dateHeureFin' => $segment->getDateHeureFin()->format('Y-m-d H:i:s'),
                    'dureeHeures' => $segment->getDureeHeures(),
                    'mission' => [
                        'id' => $segment->getMission()->getId(),
                        'titre' => $segment->getMission()->getTitre(),
                        'pays' => $segment->getMission()->getPays()
                    ]
                ];
            }, $calcul['segments'])
        ]);
    }

    /**
     * Heures supplémentaires
     */
    #[Route('/heures-supplementaires', name: 'heures_supplementaires', methods: ['GET'])]
    public function heuresSupplementaires(Request $request): JsonResponse
    {
        $userId = $request->query->get('userId');
        $dateDebut = $request->query->get('dateDebut');
        $dateFin = $request->query->get('dateFin');

        $user = $userId ? $this->userRepository->find($userId) : null;
        $dateDebutObj = $dateDebut ? new \DateTime($dateDebut) : null;
        $dateFinObj = $dateFin ? new \DateTime($dateFin) : null;

        $semainesAvecHS = $this->heureHebdomadaireService->findSemainesAvecHeuresSupplementaires(
            $user,
            $dateDebutObj,
            $dateFinObj
        );

        return $this->json($semainesAvecHS);
    }

    /**
     * Statistiques hebdomadaires
     */
    #[Route('/statistiques', name: 'statistiques', methods: ['GET'])]
    public function statistiques(Request $request): JsonResponse
    {
        $dateDebut = $request->query->get('dateDebut');
        $dateFin = $request->query->get('dateFin');

        $dateDebutObj = $dateDebut ? new \DateTime($dateDebut) : null;
        $dateFinObj = $dateFin ? new \DateTime($dateFin) : null;

        $stats = $this->heureHebdomadaireService->getStatistiquesHebdomadaires($dateDebutObj, $dateFinObj);

        return $this->json($stats);
    }

    /**
     * Comparaison avec horaire contractuel
     */
    #[Route('/comparaison-contractuel', name: 'comparaison_contractuel', methods: ['GET'])]
    public function comparaisonContractuel(Request $request): JsonResponse
    {
        $dateDebut = $request->query->get('dateDebut');
        $dateFin = $request->query->get('dateFin');

        $dateDebutObj = $dateDebut ? new \DateTime($dateDebut) : new \DateTime('-3 months');
        $dateFinObj = $dateFin ? new \DateTime($dateFin) : new \DateTime();

        $users = $this->userRepository->findActifs();
        $comparaisons = [];

        foreach ($users as $user) {
            $calcul = $this->heureHebdomadaireService->calculerHeuresPeriode($user, $dateDebutObj, $dateFinObj);

            $comparaisons[] = [
                'user' => [
                    'id' => $user->getId(),
                    'nomComplet' => $user->getNomComplet(),
                    'horaireHebdo' => $user->getHoraireHebdo(),
                    'forfaitJour' => $user->isForfaitJour()
                ],
                'moyenneReelle' => $calcul['moyenneHebdomadaire'],
                'horaireContractuel' => $user->getHoraireHebdo(),
                'ecart' => $calcul['moyenneHebdomadaire'] - $user->getHoraireHebdo(),
                'tauxRealisation' => $user->getHoraireHebdo() > 0 ?
                    ($calcul['moyenneHebdomadaire'] / $user->getHoraireHebdo()) * 100 : 0,
                'totalHeuresSupplementaires' => $calcul['totaux']['heuresSupplementaires'],
                'nombreSemaines' => $calcul['nombreSemaines']
            ];
        }

        // Trier par écart décroissant
        usort($comparaisons, function($a, $b) {
            return $b['ecart'] <=> $a['ecart'];
        });

        return $this->json([
            'periode' => [
                'dateDebut' => $dateDebutObj->format('Y-m-d'),
                'dateFin' => $dateFinObj->format('Y-m-d')
            ],
            'comparaisons' => $comparaisons
        ]);
    }

    /**
     * Export des données pour une période
     */
    #[Route('/export', name: 'export', methods: ['GET'])]
    public function export(Request $request): JsonResponse
    {
        $dateDebut = $request->query->get('dateDebut');
        $dateFin = $request->query->get('dateFin');
        $format = $request->query->get('format', 'json');

        $dateDebutObj = $dateDebut ? new \DateTime($dateDebut) : new \DateTime('-3 months');
        $dateFinObj = $dateFin ? new \DateTime($dateFin) : new \DateTime();

        $users = $this->userRepository->findActifs();
        $export = [];

        foreach ($users as $user) {
            $calcul = $this->heureHebdomadaireService->calculerHeuresPeriode($user, $dateDebutObj, $dateFinObj);

            $export[] = [
                'user' => $user->getNomComplet(),
                'email' => $user->getEmail(),
                'horaireContractuel' => $user->getHoraireHebdo(),
                'forfaitJour' => $user->isForfaitJour(),
                'periode' => [
                    'dateDebut' => $dateDebutObj->format('Y-m-d'),
                    'dateFin' => $dateFinObj->format('Y-m-d'),
                    'nombreSemaines' => $calcul['nombreSemaines']
                ],
                'totaux' => $calcul['totaux'],
                'moyenneHebdomadaire' => $calcul['moyenneHebdomadaire'],
                'detailSemaines' => array_map(function($semaine) {
                    return [
                        'semaine' => $semaine['semaineAnnee'],
                        'heures' => $semaine['heuresSaisies'],
                        'heuresNormales' => $semaine['heuresNormales'],
                        'heuresSupplementaires' => $semaine['heuresSupplementaires'],
                        'nombreSegments' => count($semaine['segments'])
                    ];
                }, $calcul['detailSemaines'])
            ];
        }

        return $this->json([
            'export' => $export,
            'metadata' => [
                'dateGeneration' => (new \DateTime())->format('Y-m-d H:i:s'),
                'nombreUsers' => count($users),
                'periode' => [
                    'debut' => $dateDebutObj->format('Y-m-d'),
                    'fin' => $dateFinObj->format('Y-m-d')
                ]
            ]
        ]);
    }
}
