{% extends 'base.html.twig' %}

{% block title %}Détail des primes - {{ detailPrimes.mission.titre }} - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête avec navigation -->
    <div class="mb-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{{ path('app_primes') }}" class="text-gray-400 hover:text-gray-500">
                            <span class="sr-only">Primes</span>
                            💰
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <a href="{{ path('app_primes') }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Primes</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                        </svg>
                        <span class="ml-4 text-sm font-medium text-gray-500">{{ detailPrimes.mission.titre }}</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <div class="mt-4">
            <h1 class="text-3xl font-bold text-gray-900">Détail des primes</h1>
            <p class="mt-2 text-gray-600">{{ detailPrimes.mission.titre }} - {{ detailPrimes.mission.pays }}</p>
        </div>
    </div>

    <!-- Informations de la mission -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Informations de la mission</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Pays</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ detailPrimes.mission.pays }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Zone</dt>
                    <dd class="mt-1">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ detailPrimes.mission.zone == 'EURO' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                            {{ detailPrimes.mission.zone == 'EURO' ? 'Zone Euro' : 'Hors Zone Euro' }}
                        </span>
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Niveau</dt>
                    <dd class="mt-1 text-sm text-gray-900">Niveau {{ detailPrimes.mission.niveau }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Période</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ detailPrimes.mission.dateDebut|date('d/m/Y') }} au {{ detailPrimes.mission.dateFin|date('d/m/Y') }}</dd>
                </div>
            </div>
        </div>
    </div>

    <!-- Résumé des primes -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-4 mb-8">
        <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="text-2xl">💰</span>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total primes</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ detailPrimes.resume.totalPrimes|number_format(2, ',', ' ') }}€</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-green-50 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="text-2xl">📅</span>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Nombre de jours</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ detailPrimes.resume.nombreJours }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="text-2xl">📊</span>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Prime moyenne/jour</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ detailPrimes.resume.primeMoyenneJour|number_format(2, ',', ' ') }}€</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-orange-50 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="text-2xl">⏱️</span>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total heures</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ detailPrimes.resume.totalHeures|number_format(1, ',', ' ') }}h</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barème applicable -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Barème applicable</h3>
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type de jour</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Montant (Niveau {{ detailPrimes.mission.niveau }})</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 bg-white">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Jour de semaine</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ detailPrimes.bareme.semaine[detailPrimes.mission.niveau] }}€</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Week-end travaillé</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ detailPrimes.bareme.weekend_travaille[detailPrimes.mission.niveau] }}€</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Week-end non travaillé</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ detailPrimes.bareme.weekend_non_travaille[detailPrimes.mission.niveau] }}€</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Week-end voyage</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ detailPrimes.bareme.weekend_voyage[detailPrimes.mission.niveau] }}€</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Détail jour par jour -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Détail jour par jour</h3>
            
            <div class="space-y-6">
                {% for jour in detailPrimes.detailJours %}
                <div class="border border-gray-200 rounded-lg p-4 {{ jour.isWeekend ? 'bg-blue-50' : 'bg-white' }}">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <h4 class="text-lg font-medium text-gray-900">
                                {{ jour.jourSemaine }} {{ jour.dateFormatee }}
                                {% if jour.isWeekend %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">Week-end</span>
                                {% endif %}
                            </h4>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-500">{{ jour.typeJourLibelle }}</div>
                            <div class="text-lg font-bold text-gray-900">{{ jour.prime|number_format(2, ',', ' ') }}€</div>
                        </div>
                    </div>
                    
                    {% if jour.segments|length > 0 %}
                    <div class="mt-4">
                        <h5 class="text-sm font-medium text-gray-700 mb-2">Segments ({{ jour.heuresTotal|number_format(1, ',', ' ') }}h total)</h5>
                        <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
                            {% for segment in jour.segments %}
                            <div class="bg-white border border-gray-200 rounded p-3">
                                <div class="flex items-center justify-between">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                        {{ segment.type == 'VOYAGE' ? 'bg-red-100 text-red-800' : 
                                           segment.type == 'INTERVENTION' ? 'bg-green-100 text-green-800' : 
                                           'bg-yellow-100 text-yellow-800' }}">
                                        {{ segment.type }}
                                    </span>
                                    <span class="text-sm text-gray-500">{{ segment.heures|number_format(1, ',', ' ') }}h</span>
                                </div>
                                <div class="mt-1 text-sm text-gray-600">{{ segment.debut }} - {{ segment.fin }}</div>
                                {% if segment.user %}
                                <div class="mt-1 text-xs text-gray-500">{{ segment.user.nom }}</div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% else %}
                    <div class="mt-4 text-sm text-gray-500 italic">Aucun segment pour ce jour</div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
