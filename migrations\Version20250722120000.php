<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Suppression du champ role et rendre horaireHebdo optionnel
 */
final class Version20250722120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Supprime le champ role de la table user et rend le champ horaire_hebdo nullable';
    }

    public function up(Schema $schema): void
    {
        // Supprimer la colonne role
        $this->addSql('ALTER TABLE `user` DROP role');
        
        // Rendre la colonne horaire_hebdo nullable
        $this->addSql('ALTER TABLE `user` MODIFY horaire_hebdo DOUBLE PRECISION DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // Recréer la colonne role
        $this->addSql('ALTER TABLE `user` ADD role VARCHAR(50) NOT NULL DEFAULT "Consultant"');
        
        // Rendre la colonne horaire_hebdo obligatoire
        $this->addSql('ALTER TABLE `user` MODIFY horaire_hebdo DOUBLE PRECISION NOT NULL');
    }
}
