{% extends 'base.html.twig' %}

{% block title %}Gestion des primes - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0"
     data-controller="primes-management"
     data-primes-management-users-api-url-value="{{ path('api_user_index') }}"
     data-primes-management-missions-api-url-value="{{ path('api_mission_index') }}"
     data-primes-management-mission-primes-url-value="{{ path('api_mission_primes', {id: 'MISSION_ID'}) }}"
     data-primes-management-prime-detail-url-value="{{ path('app_prime_detail', {id: 'MISSION_ID'}) }}">
    <!-- En-tête -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Gestion des primes</h1>
        <p class="mt-2 text-gray-600">Calcul et suivi des primes journalières</p>
        <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p class="text-sm text-blue-800">
                <span class="font-medium">📅 Affichage automatique :</span>
                Les primes du mois en cours sont calculées automatiquement au chargement de la page.
            </p>
        </div>
    </div>

    <!-- Barème des primes -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Barème des primes journalières</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Zone Euro -->
                <div>
                    <h4 class="text-md font-medium text-blue-900 mb-3">Zone Euro</h4>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-blue-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 1</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 2</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">Semaine</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.semaine[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.semaine[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE non travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_non_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_non_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE voyage</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_voyage[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.EURO.weekend_voyage[2] }}€</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Hors Zone Euro -->
                <div>
                    <h4 class="text-md font-medium text-green-900 mb-3">Hors Zone Euro</h4>
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-green-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 1</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Niveau 2</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">Semaine</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.semaine[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.semaine[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE non travaillé</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_non_travaille[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_non_travaille[2] }}€</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 text-sm text-gray-900">WE voyage</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_voyage[1] }}€</td>
                                    <td class="px-3 py-2 text-sm text-gray-900">{{ bareme.HORS_EURO.weekend_voyage[2] }}€</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-6">
                <div>
                    <label for="userPrimes" class="block text-sm font-medium text-gray-700">Utilisateur</label>
                    <select id="userPrimes" data-primes-management-target="userPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les utilisateurs</option>
                    </select>
                </div>
                <div>
                    <label for="zonePrimes" class="block text-sm font-medium text-gray-700">Zone</label>
                    <select id="zonePrimes" data-primes-management-target="zonePrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Toutes les zones</option>
                        <option value="EURO">Zone Euro</option>
                        <option value="HORS_EURO">Hors Zone Euro</option>
                    </select>
                </div>
                <div>
                    <label for="niveauPrimes" class="block text-sm font-medium text-gray-700">Niveau</label>
                    <select id="niveauPrimes" data-primes-management-target="niveauPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les niveaux</option>
                        <option value="1">Niveau 1</option>
                        <option value="2">Niveau 2</option>
                    </select>
                </div>
                <div>
                    <label for="dateDebutPrimes" class="block text-sm font-medium text-gray-700">Date début</label>
                    <input type="date" id="dateDebutPrimes" data-primes-management-target="dateDebutPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="dateFinPrimes" class="block text-sm font-medium text-gray-700">Date fin</label>
                    <input type="date" id="dateFinPrimes" data-primes-management-target="dateFinPrimes" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div class="flex items-end space-x-3">
                    <button type="button" data-action="click->primes-management#calculerPrimes" data-primes-management-target="calculerButton" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg data-primes-management-target="loadingSpinner" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white hidden" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span data-primes-management-target="calculerText">Calculer</span>
                    </button>
                    <button type="button" data-action="click->primes-management#calculerPrimesMoisEnCours" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50">
                        📅 Mois en cours
                    </button>
                </div>
            </div>

            <!-- Toggle pour masquer les missions à 0€ -->
            <div class="flex items-center space-x-3 mt-4 pt-4 border-t border-gray-200">
                <div class="flex items-center">
                    <button type="button" id="toggleMissionsZero" data-primes-management-target="toggleMissionsZero" data-action="click->primes-management#toggleMasquerMissionsZero"
                            class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2"
                            role="switch" aria-checked="true">
                        <span class="sr-only">Masquer les missions à 0€</span>
                        <span id="toggleMissionsZeroThumb" data-primes-management-target="toggleMissionsZeroThumb" class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out translate-x-5"></span>
                    </button>
                    <span class="ml-3 text-sm text-gray-700" id="toggleMissionsZeroLabel" data-primes-management-target="toggleMissionsZeroLabel">Masquer les missions à 0€</span>
                    <input type="hidden" id="masquerMissionsZero" data-primes-management-target="masquerMissionsZero" value="true">
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats des primes -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Calcul des primes</h3>

            <!-- Résumé -->
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-3 mb-6" id="resumePrimes" style="display: none;">
                <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">💰</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total primes</dt>
                                    <dd class="text-lg font-medium text-gray-900" id="totalPrimes">0€</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">📊</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Assignations</dt>
                                    <dd class="text-lg font-medium text-gray-900" id="nombreMissions">0</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <span class="text-2xl">⏱️</span>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Heures réelles / théoriques</dt>
                                    <dd class="text-sm font-medium text-gray-900" id="primeMoyenne">0h / 0h</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tableau détaillé -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200" id="primesTable" data-primes-management-target="primesTable" style="display: none;">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mission</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pays</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zone</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Niveau</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heures réelles</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prime individuelle</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="primesTableBody" data-primes-management-target="primesTableBody">
                        <!-- Les données seront chargées dynamiquement -->
                    </tbody>
                </table>
            </div>

            <div id="noPrimesMessage" data-primes-management-target="noPrimesMessage" class="text-center py-8 text-gray-500">
                Sélectionnez des critères et cliquez sur "Calculer" pour voir les primes
            </div>
        </div>
    </div>
</div>

{% endblock %}
