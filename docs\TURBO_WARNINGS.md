# Warnings Turbo - Import Map Conflicts

## 🚨 Warnings observés

```
An import map rule for specifier 'X' was removed, as it conflicted with already resolved module specifiers.
```

## 🔍 Cause du problème

Ces warnings apparaissent à cause de :

1. **Mode debug activé** : Symfony AssetMapper recompile les assets à chaque requête
2. **Navigation Turbo** : Les modules sont rechargés sans refresh complet de la page
3. **Import map dynamique** : Conflits entre modules déjà chargés et nouveaux modules

## ✅ Solutions appliquées

### 1. Configuration Turbo optimisée
- Ajout de `assets/js/turbo-config.js`
- Gestion des événements Turbo
- Suppression des warnings en production

### 2. Gestion d'erreurs Stimulus améliorée
- Configuration dans `bootstrap.js`
- Logs conditionnels selon l'environnement
- Gestion centralisée des erreurs

### 3. Script de nettoyage automatique
- `scripts/clean-assets.bat` pour Windows
- Suppression et recompilation automatique
- Utilisation : `scripts\clean-assets.bat`

## 🛠️ Actions de maintenance

### Nettoyage manuel
```bash
# Supprimer les assets compilés
rmdir /s /q public\assets

# Recompiler
php bin/console asset-map:compile
```

### Nettoyage automatique
```bash
# Utiliser le script
scripts\clean-assets.bat
```

## 📊 Impact sur les performances

- **Développement** : Warnings visibles mais sans impact fonctionnel
- **Production** : Warnings masqués automatiquement
- **Navigation** : Aucun impact sur la vitesse de navigation

## 🔮 Évolution future

Ces warnings devraient disparaître avec :
- **Symfony 7.1+** : Améliorations de l'AssetMapper
- **Mode production** : Import map statique
- **Turbo 8** : Meilleure gestion des modules ES6

## ✨ Bonnes pratiques

1. **Ignorer en développement** : Ces warnings n'affectent pas le fonctionnement
2. **Nettoyer régulièrement** : Utiliser le script de nettoyage
3. **Tester en production** : Vérifier que les warnings sont masqués
4. **Surveiller les performances** : S'assurer que la navigation reste fluide
