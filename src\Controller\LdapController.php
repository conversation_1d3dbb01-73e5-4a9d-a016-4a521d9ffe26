<?php

namespace App\Controller;

use App\Service\LdapService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use App\Repository\UserRepository;

class LdapController extends AbstractController
{
    private $ldapService;
    private $userRepository;

    public function __construct(LdapService $ldapService, UserRepository $userRepository)
    {
        $this->ldapService = $ldapService;
        $this->userRepository = $userRepository; // Injection correcte
    }
    
    #[Route('/ldap/groups', name: 'ldap_groups')]
    public function getGroups(Request $request): JsonResponse
    {
        $targetUsername = $request->query->get('username'); // Récupère le paramètre 'username' s'il est présent
        $user = $this->getUser();
        $requiredRoles = [
            'ROLE_USER_INFORMATIQUE',
        ];
        
        if (!$user->hasManagerRoleOrAnyRole($requiredRoles)) {
            return $this->json(['error' => 'Vous n\'avez pas les droits pour accéder à cette ressource'], 403);
        }
        
        // Si un 'username' est passé dans la requête, on l'utilise, sinon on prend l'email de l'utilisateur connecté
        $username = $targetUsername ?: $user->getUsername();
        $prenom = strtoupper($user->getPrenom());
        $nom = strtoupper($user->getNom());
        // Si un 'username' spécifique a été passé, on doit aussi récupérer son prénom et nom depuis la BD
        if ($targetUsername && $targetUsername !== $user->getEmail()) {
            $userInfo = $this->userRepository->findOneBy(['username' => $targetUsername]);
            if ($userInfo) {
                $prenom = strtoupper($userInfo->getPrenom());
                $nom = strtoupper($userInfo->getNom());
            } else {
                // Gérer le cas où l'utilisateur n'est pas trouvé
                return $this->json(['error' => 'Utilisateur non trouvé'], 404);
            }
        }

        $baseDn = 'OU=FRSCMFS01,OU=FRSCM_Groupes,DC=scmlemans,DC=com';
        $filter = '(objectClass=*)';
        $groups = $this->ldapService->search($baseDn, $filter, ['name', 'distinguishedName','description']);

        $results = [];
        $tableResults = [];
        foreach ($groups as $group) {
            // dd($groups[1]->getAttribute('distinguishedName')[0] ,$groups[1]->getAttribute('description')[0]);
            $groupName = isset($group->getAttribute('name')[0]) ? $group->getAttribute('name')[0] : 'Unknown';
            $groupDn = isset($group->getAttribute('distinguishedName')[0]) ? $group->getAttribute('distinguishedName')[0] : 'Unknown';
            $groupDescription = isset($group->getAttribute('description')[0]) ? $group->getAttribute('description')[0] : 'No description';
            $nomPrenom = $prenom.' '.$nom;
            $ownerName = $this->extractOwnerFromDescription($groupDescription);
            // Appel de la méthode récursive pour récupérer tous les membres
            $membersInfo = $this->getGroupMembersRecursive($groupDn);

            $results[] = [
                'group_name' => $groupName,
                'group_dn' => $groupDn,
                'group_description' => $groupDescription,
                'members' => $membersInfo,
            ];

            foreach ($membersInfo as $member) {
                // dd($ownerName, $nomPrenom);
                // if (!isset($tableResults[$groupName]) &&((isset($member['manager']) && is_array($member['manager']) && isset($member['manager']['username']) && $member['manager']['username'] === 'acrapis') || $ownerName === 'ANTOINE CRAPIS') ) {
                if (!isset($tableResults[$groupName]) &&((isset($member['manager']) && is_array($member['manager']) && isset($member['manager']['username']) && $member['manager']['username'] === $username) || $ownerName === $nomPrenom) ) {    
                    $tableResults[$groupName] = [
                        'group_name' => $groupName,
                        'group_dn' => $groupDn,
                        'group_description' => $groupDescription,
                        'members' => [],
                        'other_members' => [],
                    ];
                }
                // if (isset($member['manager']) && is_array($member['manager']) && isset($member['manager']['username']) && $member['manager']['username'] === 'acrapis') {
                if (isset($member['manager']) && is_array($member['manager']) && isset($member['manager']['username']) && $member['manager']['username'] === $username) {
                    $tableResults[$groupName]['members'][] = $member;
                }
                // dd($ownerName);
                // if($ownerName === 'ANTOINE CRAPIS'){
                if($ownerName === $prenom.' '.$nom){
                    if (!in_array($member, $tableResults[$groupName]['members'], true)) {
                        $tableResults[$groupName]['other_members'][] = $member;
                    }
                }
            }
        }
        $filteredResults = array_values($tableResults);
        return $this->json($filteredResults);
    }

    private function extractOwnerFromDescription(string $description): ?string
    {
        // Regex pour extraire le nom après "Owner :"
        preg_match('/Owner\s*:\s*([\w\s]+)/', $description, $matches);

        if (isset($matches[1])) {
            return strtoupper($matches[1]); 
        }

        return null;  // Si aucun propriétaire n'est trouvé
    }


    private function getGroupMembersRecursive(string $groupDn, array &$visitedGroups = []): array
    {
        if (in_array($groupDn, $visitedGroups)) {
            return [];
        }
        $visitedGroups[] = $groupDn;

        // Cas spécial pour all_scm
        if ($groupDn === 'CN=ALL_SCM,OU=FRSCMFS01,OU=FRSCM_Groupes,DC=scmlemans,DC=com') {
            // return $this->ldapService->getAllScmMembers($groupDn);
            return $membersInfo = ['All SCM'];
        }

        $groupMembers = $this->ldapService->search($groupDn, '(&(objectClass=group)(distinguishedName=' . $groupDn . '))', ['member']);
        $membersInfo = [];

        if (!empty($groupMembers) && isset($groupMembers[0])) {
            $members = $groupMembers[0]->getAttribute('member') ?? [];
            foreach ($members as $memberDn) {
                $userInfo = $this->ldapService->getUserInfo($memberDn);
                if ($userInfo === null) {
                    // Log ou message d'erreur si aucune info n'est trouvée
                    continue;    
                }
                elseif ( $userInfo['isContainer']) {
                    $subMembersInfo = $this->getGroupMembersRecursive($memberDn, $visitedGroups);
                    $membersInfo = array_merge($membersInfo, $subMembersInfo);
                } else {
                    $membersInfo[] = $userInfo;
                }
            }
        }

        return $membersInfo;
    }

    
    #[Route('/ldap/specific-users', name: 'ldap_specific_users')]
    public function getSpecificUsers(): JsonResponse
    {
        // Les chemins DN des OU à explorer
        $ouPaths = [
            'OU=FRSCM_Utilisateurs_Standard,OU=FRSCM_Utilisateurs,DC=scmlemans,DC=com'
        ];

        $users = $this->ldapService->getUsersFromSpecificOUs($ouPaths);

        return $this->json($users);
    }
    

    #[Route('ldap/all-users_bd', name: 'ldap_all_users_bd')]
    public function getAllUsers(UserRepository $userRepository): Response
    {

        $user = $this->getUser();
        $requiredRoles = [
            'ROLE_USER_INFORMATIQUE',
        ];
        
        if (!$user->hasManagerRoleOrAnyRole($requiredRoles)) {
            return $this->json(['error' => 'Vous n\'avez pas les droits pour accéder à cette ressource'], 403);
        }
        $users = $userRepository->findAll();

        $userData = [];

        foreach ($users as $user) {
            if($user-> getIsManager() == 1){
                $userData[] = [
                    'username' => $user->getUsername(), 
                    'nom' => $user->getNom(),           
                    'prenom' => $user->getPrenom(),     
                ];
            }
        }

        return $this->json($userData);
    }
    
        
}
