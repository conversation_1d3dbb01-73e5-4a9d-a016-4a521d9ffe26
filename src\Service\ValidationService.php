<?php

namespace App\Service;

use App\Entity\Segment;
use App\Entity\Mission;

class ValidationService
{
    private HeureCalculatorService $heureCalculator;

    public function __construct(HeureCalculatorService $heureCalculator)
    {
        $this->heureCalculator = $heureCalculator;
    }

    /**
     * Valide un segment
     */
    public function validerSegment(Segment $segment, array $autresSegments = []): array
    {
        $erreurs = [];
        $alertes = [];

        // Validation de base : date fin > date début
        if ($segment->getDateHeureFin() <= $segment->getDateHeureDebut()) {
            $erreurs[] = 'La date de fin doit être postérieure à la date de début.';
        }

        // Validation des chevauchements avec d'autres segments
        foreach ($autresSegments as $autreSegment) {
            if ($autreSegment->getId() !== $segment->getId() && $this->segmentsChevauchent($segment, $autreSegment)) {
                $erreurs[] = sprintf(
                    'Ce segment chevauche avec un autre segment %s du %s au %s.',
                    $autreSegment->getType(),
                    $autreSegment->getDateHeureDebut()->format('d/m/Y H:i'),
                    $autreSegment->getDateHeureFin()->format('d/m/Y H:i')
                );
            }
        }

        // Validation du repos post-voyage
        if ($segment->isVoyage()) {
            $alertesRepos = $this->validerReposPostVoyage($segment);
            $alertes = array_merge($alertes, $alertesRepos);
        }

        return [
            'valide' => empty($erreurs),
            'erreurs' => $erreurs,
            'alertes' => $alertes
        ];
    }

    /**
     * Valide le repos post-voyage
     */
    public function validerReposPostVoyage(Segment $voyage): array
    {
        $alertes = [];

        $dureeVoyageH = $this->heureCalculator->getDureeHeures($voyage);
        $decalageHoraire = $this->estimerDecalageHoraire($voyage->getMission());

        // Règle : si durée >= 10h OU décalage >= 5h, alors repos >= 11h
        if ($dureeVoyageH >= 10 || $decalageHoraire >= 5) {
            $reposNecessaire = 11;

            // Vérifier s'il y a un segment suivant
            $segmentSuivant = $this->trouverSegmentSuivant($voyage);

            if ($segmentSuivant) {
                $reposEffectif = $this->calculerRepos($voyage, $segmentSuivant);

                if ($reposEffectif < $reposNecessaire) {
                    $alertes[] = sprintf(
                        'Repos insuffisant après voyage : %s heures (minimum requis : %s heures). Durée voyage : %s heures, Décalage horaire : %s heures.',
                        number_format($reposEffectif, 1),
                        $reposNecessaire,
                        number_format($dureeVoyageH, 1),
                        $decalageHoraire
                    );
                }
            } else {
                $alertes[] = sprintf(
                    'Attention : voyage de %s heures avec décalage de %s heures nécessite un repos de %s heures minimum.',
                    number_format($dureeVoyageH, 1),
                    $decalageHoraire,
                    $reposNecessaire
                );
            }
        }

        return $alertes;
    }

    /**
     * Valide une mission complète
     */
    public function validerMission(Mission $mission): array
    {
        $erreurs = [];
        $alertes = [];

        // Validation des dates de mission
        if ($mission->getDateFin() <= $mission->getDateDebut()) {
            $erreurs[] = 'La date de fin de mission doit être postérieure à la date de début.';
        }

        // Validation des segments
        foreach ($mission->getSegments() as $segment) {
            $validationSegment = $this->validerSegment($segment);
            $erreurs = array_merge($erreurs, $validationSegment['erreurs']);
            $alertes = array_merge($alertes, $validationSegment['alertes']);

            // Vérifier que le segment est dans les dates de mission
            if ($segment->getDateHeureDebut()->format('Y-m-d') < $mission->getDateDebut()->format('Y-m-d') ||
                $segment->getDateHeureFin()->format('Y-m-d') > $mission->getDateFin()->format('Y-m-d')) {
                $erreurs[] = sprintf(
                    'Le segment %s est en dehors des dates de mission (%s - %s).',
                    $segment->getType(),
                    $mission->getDateDebut()->format('d/m/Y'),
                    $mission->getDateFin()->format('d/m/Y')
                );
            }
        }

        // Vérifier les chevauchements de segments
        $chevauchements = $this->detecterChevauchements($mission->getSegments()->toArray());
        if (!empty($chevauchements)) {
            foreach ($chevauchements as $chevauchement) {
                $erreurs[] = sprintf(
                    'Chevauchement détecté entre les segments %s et %s.',
                    $chevauchement['segment1']->getType(),
                    $chevauchement['segment2']->getType()
                );
            }
        }

        return [
            'valide' => empty($erreurs),
            'erreurs' => $erreurs,
            'alertes' => $alertes
        ];
    }

    /**
     * Estime le décalage horaire selon le pays
     */
    private function estimerDecalageHoraire(Mission $mission): int
    {
        // Mapping simplifié des décalages horaires par pays
        $decalages = [
            'France' => 0,
            'Allemagne' => 0,
            'Espagne' => 0,
            'Italie' => 0,
            'Royaume-Uni' => 1,
            'États-Unis' => 6,
            'Canada' => 6,
            'Japon' => 8,
            'Chine' => 7,
            'Australie' => 9,
            'Brésil' => 4,
            'Inde' => 4,
        ];

        return $decalages[$mission->getPays()] ?? 0;
    }

    /**
     * Trouve le segment suivant après un voyage
     */
    private function trouverSegmentSuivant(Segment $voyage): ?Segment
    {
        $mission = $voyage->getMission();
        $segments = $mission->getSegments()->toArray();

        // Trier par date de début
        usort($segments, function($a, $b) {
            return $a->getDateHeureDebut() <=> $b->getDateHeureDebut();
        });

        $found = false;
        foreach ($segments as $segment) {
            if ($found && $segment !== $voyage) {
                return $segment;
            }
            if ($segment === $voyage) {
                $found = true;
            }
        }

        return null;
    }

    /**
     * Calcule le temps de repos entre deux segments
     */
    private function calculerRepos(Segment $segment1, Segment $segment2): float
    {
        $finSegment1 = $segment1->getDateHeureFin();
        $debutSegment2 = $segment2->getDateHeureDebut();

        if ($debutSegment2 <= $finSegment1) {
            return 0; // Pas de repos, segments se chevauchent
        }

        return ($debutSegment2->getTimestamp() - $finSegment1->getTimestamp()) / 3600;
    }

    /**
     * Détecte les chevauchements entre segments
     */
    private function detecterChevauchements(array $segments): array
    {
        $chevauchements = [];

        for ($i = 0; $i < count($segments); $i++) {
            for ($j = $i + 1; $j < count($segments); $j++) {
                $segment1 = $segments[$i];
                $segment2 = $segments[$j];

                if ($this->segmentsChevauchent($segment1, $segment2)) {
                    $chevauchements[] = [
                        'segment1' => $segment1,
                        'segment2' => $segment2
                    ];
                }
            }
        }

        return $chevauchements;
    }

    /**
     * Vérifie si deux segments se chevauchent
     */
    private function segmentsChevauchent(Segment $segment1, Segment $segment2): bool
    {
        return $segment1->getDateHeureDebut() < $segment2->getDateHeureFin() &&
               $segment2->getDateHeureDebut() < $segment1->getDateHeureFin();
    }

    /**
     * Valide les contraintes métier globales
     */
    public function validerContraintesMetier(array $missions): array
    {
        $alertes = [];

        // Vérifier les missions simultanées pour un même collaborateur
        foreach ($missions as $mission1) {
            foreach ($missions as $mission2) {
                if ($mission1 !== $mission2 &&
                    $mission1->getCollaborateur() === $mission2->getCollaborateur()) {

                    if ($this->missionsChevauchent($mission1, $mission2)) {
                        $alertes[] = sprintf(
                            'Le collaborateur %s a des missions qui se chevauchent : "%s" et "%s".',
                            $mission1->getCollaborateur()->getNomComplet(),
                            $mission1->getTitre(),
                            $mission2->getTitre()
                        );
                    }
                }
            }
        }

        return $alertes;
    }

    /**
     * Vérifie si deux missions se chevauchent
     */
    private function missionsChevauchent(Mission $mission1, Mission $mission2): bool
    {
        return $mission1->getDateDebut() <= $mission2->getDateFin() &&
               $mission2->getDateDebut() <= $mission1->getDateFin();
    }
}
