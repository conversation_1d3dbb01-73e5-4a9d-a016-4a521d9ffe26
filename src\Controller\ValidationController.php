<?php

namespace App\Controller;

use App\Entity\Segment;
use App\Entity\Prime;
use App\Service\SegmentValidationService;
use App\Service\PrimeManagementService;
use App\Repository\SegmentRepository;
use App\Repository\PrimeRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/validation')]
#[IsGranted('ROLE_USER')]
class ValidationController extends AbstractController
{
    public function __construct(
        private SegmentValidationService $segmentValidationService,
        private PrimeManagementService $primeManagementService,
        private SegmentRepository $segmentRepository,
        private PrimeRepository $primeRepository
    ) {}

    #[Route('/segments', name: 'app_validation_segments')]
    public function segments(): Response
    {
        $user = $this->getUser();
        
        if (!$user->canValidateSegments()) {
            throw $this->createAccessDeniedException('Accès refusé : vous n\'avez pas les droits pour valider des segments');
        }

        $segmentsEnAttente = $this->segmentValidationService->getSegmentsEnAttenteValidation();
        $statistiques = $this->segmentValidationService->getStatistiquesValidation();

        return $this->render('validation/segments.html.twig', [
            'segments' => $segmentsEnAttente,
            'statistiques' => $statistiques,
        ]);
    }

    #[Route('/segments/{id}/valider', name: 'app_validation_segment_valider', methods: ['POST'])]
    public function validerSegment(Segment $segment): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $this->segmentValidationService->validerSegment($segment, $user);
            
            return $this->json([
                'success' => true,
                'message' => 'Segment validé avec succès'
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/segments/{id}/invalider', name: 'app_validation_segment_invalider', methods: ['POST'])]
    public function invaliderSegment(Segment $segment): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $this->segmentValidationService->invaliderSegment($segment, $user);
            
            return $this->json([
                'success' => true,
                'message' => 'Segment invalidé avec succès'
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/segments/lot', name: 'app_validation_segments_lot', methods: ['POST'])]
    public function validerSegmentsLot(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $segmentIds = $request->request->all('segment_ids');
        
        if (empty($segmentIds)) {
            return $this->json([
                'success' => false,
                'message' => 'Aucun segment sélectionné'
            ], 400);
        }

        try {
            $segments = $this->segmentRepository->findBy(['id' => $segmentIds]);
            $count = $this->segmentValidationService->validerSegmentsEnLot($segments, $user);
            
            return $this->json([
                'success' => true,
                'message' => "$count segments validés avec succès"
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/primes', name: 'app_validation_primes')]
    public function primes(): Response
    {
        $user = $this->getUser();
        
        if (!$user->canValidatePrimesResponsable() && !$user->canValidatePrimesRh()) {
            throw $this->createAccessDeniedException('Accès refusé : vous n\'avez pas les droits pour valider des primes');
        }

        $primes = $this->primeManagementService->getPrimesAccessibles($user);
        $statistiques = $this->primeManagementService->getStatistiquesPrimes($user);

        return $this->render('validation/primes.html.twig', [
            'primes' => $primes,
            'statistiques' => $statistiques,
            'user_role' => $user->getRoleMetier(),
        ]);
    }

    #[Route('/primes/{id}/valider-responsable', name: 'app_validation_prime_responsable', methods: ['POST'])]
    public function validerPrimeResponsable(Prime $prime): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $this->primeManagementService->validerPrimeParResponsable($prime, $user);
            
            return $this->json([
                'success' => true,
                'message' => 'Prime validée par le responsable avec succès'
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/primes/{id}/invalider-responsable', name: 'app_validation_prime_responsable_invalider', methods: ['POST'])]
    public function invaliderPrimeResponsable(Prime $prime): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $this->primeManagementService->invaliderPrimeParResponsable($prime, $user);
            
            return $this->json([
                'success' => true,
                'message' => 'Prime invalidée par le responsable avec succès'
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/primes/{id}/valider-rh', name: 'app_validation_prime_rh', methods: ['POST'])]
    public function validerPrimeRh(Prime $prime): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $this->primeManagementService->validerPrimeParRh($prime, $user);
            
            return $this->json([
                'success' => true,
                'message' => 'Prime validée par RH avec succès'
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/primes/{id}/invalider-rh', name: 'app_validation_prime_rh_invalider', methods: ['POST'])]
    public function invaliderPrimeRh(Prime $prime): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $this->primeManagementService->invaliderPrimeParRh($prime, $user);
            
            return $this->json([
                'success' => true,
                'message' => 'Prime invalidée par RH avec succès'
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/primes/{id}/recalculer', name: 'app_validation_prime_recalculer', methods: ['POST'])]
    public function recalculerPrime(Prime $prime): JsonResponse
    {
        $user = $this->getUser();
        
        if (!$user->canValidatePrimesResponsable()) {
            return $this->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        try {
            $this->primeManagementService->recalculerPrime($prime);
            
            return $this->json([
                'success' => true,
                'message' => 'Prime recalculée avec succès',
                'nouveau_montant' => $prime->getMontantFloat()
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/dashboard', name: 'app_validation_dashboard')]
    public function dashboard(): Response
    {
        $user = $this->getUser();
        
        $data = [];
        
        if ($user->canValidateSegments()) {
            $data['segments'] = $this->segmentValidationService->getStatistiquesValidation();
        }
        
        if ($user->canValidatePrimesResponsable() || $user->canValidatePrimesRh()) {
            $data['primes'] = $this->primeManagementService->getStatistiquesPrimes($user);
        }

        return $this->render('validation/dashboard.html.twig', [
            'data' => $data,
            'user_role' => $user->getRoleMetier(),
        ]);
    }
}
