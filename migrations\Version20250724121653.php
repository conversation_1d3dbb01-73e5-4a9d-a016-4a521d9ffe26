<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250724121653 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_C5B4C2VALIDATEUR_OSI');
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_C5B4C2VALIDATEUR_RH');
        $this->addSql('DROP INDEX IDX_C5B4C2VALIDATEUR_OSI ON prime');
        $this->addSql('DROP INDEX IDX_C5B4C2VALIDATEUR_RH ON prime');
        $this->addSql('DROP INDEX UNIQ_PRIME_USER_MISSION_MOIS ON prime');
        $this->addSql('ALTER TABLE prime ADD valide_par_responsable_osi_id INT DEFAULT NULL, ADD valide_par_assistante_rh_id INT DEFAULT NULL, ADD montant NUMERIC(10, 2) NOT NULL, ADD periode_fin DATE NOT NULL, ADD date_creation DATETIME NOT NULL, ADD date_modification DATETIME DEFAULT NULL, DROP validateur_responsable_osi_id, DROP validateur_assistante_rh_id, DROP montant_total, DROP detail_calcul, DROP created_at, DROP updated_at, CHANGE validation_responsable_osi validation_responsable_osi TINYINT(1) DEFAULT NULL, CHANGE validation_assistante_rh validation_assistante_rh TINYINT(1) DEFAULT NULL, CHANGE mois periode_debut DATE NOT NULL');
        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_544B0F57F2B29BA1 FOREIGN KEY (valide_par_responsable_osi_id) REFERENCES `user` (id)');
        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_544B0F571321B12E FOREIGN KEY (valide_par_assistante_rh_id) REFERENCES `user` (id)');
        $this->addSql('CREATE INDEX IDX_544B0F57F2B29BA1 ON prime (valide_par_responsable_osi_id)');
        $this->addSql('CREATE INDEX IDX_544B0F571321B12E ON prime (valide_par_assistante_rh_id)');
        $this->addSql('ALTER TABLE prime RENAME INDEX idx_c5b4c2a76ed395 TO IDX_544B0F57A76ED395');
        $this->addSql('ALTER TABLE prime RENAME INDEX idx_c5b4c2be6cae90 TO IDX_544B0F57BE6CAE90');
        $this->addSql('ALTER TABLE segment DROP FOREIGN KEY FK_1881F565C5C0D3E');
        $this->addSql('DROP INDEX IDX_1881F565C5C0D3E ON segment');
        $this->addSql('ALTER TABLE segment CHANGE valide valide TINYINT(1) DEFAULT NULL, CHANGE validateur_id valide_par_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE segment ADD CONSTRAINT FK_1881F5656AF12ED9 FOREIGN KEY (valide_par_id) REFERENCES `user` (id)');
        $this->addSql('CREATE INDEX IDX_1881F5656AF12ED9 ON segment (valide_par_id)');
        $this->addSql('ALTER TABLE user ADD role_metier VARCHAR(50) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_544B0F57F2B29BA1');
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_544B0F571321B12E');
        $this->addSql('DROP INDEX IDX_544B0F57F2B29BA1 ON prime');
        $this->addSql('DROP INDEX IDX_544B0F571321B12E ON prime');
        $this->addSql('ALTER TABLE prime ADD validateur_responsable_osi_id INT DEFAULT NULL, ADD validateur_assistante_rh_id INT DEFAULT NULL, ADD mois DATE NOT NULL, ADD montant_total DOUBLE PRECISION NOT NULL, ADD detail_calcul JSON NOT NULL, ADD created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, ADD updated_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL, DROP valide_par_responsable_osi_id, DROP valide_par_assistante_rh_id, DROP montant, DROP periode_debut, DROP periode_fin, DROP date_creation, DROP date_modification, CHANGE validation_responsable_osi validation_responsable_osi TINYINT(1) DEFAULT 0, CHANGE validation_assistante_rh validation_assistante_rh TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_C5B4C2VALIDATEUR_OSI FOREIGN KEY (validateur_responsable_osi_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_C5B4C2VALIDATEUR_RH FOREIGN KEY (validateur_assistante_rh_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE INDEX IDX_C5B4C2VALIDATEUR_OSI ON prime (validateur_responsable_osi_id)');
        $this->addSql('CREATE INDEX IDX_C5B4C2VALIDATEUR_RH ON prime (validateur_assistante_rh_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_PRIME_USER_MISSION_MOIS ON prime (user_id, mission_id, mois)');
        $this->addSql('ALTER TABLE prime RENAME INDEX idx_544b0f57a76ed395 TO IDX_C5B4C2A76ED395');
        $this->addSql('ALTER TABLE prime RENAME INDEX idx_544b0f57be6cae90 TO IDX_C5B4C2BE6CAE90');
        $this->addSql('ALTER TABLE segment DROP FOREIGN KEY FK_1881F5656AF12ED9');
        $this->addSql('DROP INDEX IDX_1881F5656AF12ED9 ON segment');
        $this->addSql('ALTER TABLE segment CHANGE valide valide TINYINT(1) DEFAULT 0, CHANGE valide_par_id validateur_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE segment ADD CONSTRAINT FK_1881F565C5C0D3E FOREIGN KEY (validateur_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE INDEX IDX_1881F565C5C0D3E ON segment (validateur_id)');
        $this->addSql('ALTER TABLE `user` DROP role_metier');
    }
}
