{% extends 'base.html.twig' %}

{% block title %}Utilisateurs{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
/* Conteneur de sélection */
.user-selection-container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Éléments sélectionnables */
.user-item {
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-item:hover {
    background-color: #f3f4f6;
}

.user-item.selected {
    background-color: #dbeafe !important;
    border-left: 4px solid #3b82f6;
}

.user-item.selecting {
    background-color: #e0e7ff !important;
}

/* Boîte de sélection visuelle */
.drag-selection-box {
    position: absolute;
    border: 2px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    pointer-events: none;
    z-index: 1000;
    border-radius: 4px;
    transition: opacity 0.1s ease;
}

/* État de drag */
.dragging {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    cursor: crosshair !important;
}

.dragging * {
    cursor: crosshair !important;
}
</style>
{% endblock %}

{% block body %}
<div class="container mx-auto px-4 py-8"
     data-controller="user-management main-selection modal-selection"
     data-user-management-sync-url-value="{{ path('api_ldap_sync_load_all_users') }}"
     data-user-management-delete-url-value="{{ path('api_user_delete', {id: '__ID__'}) }}"
     data-user-management-show-url-value="{{ path('api_user_show', {id: '__ID__'}) }}"
     data-user-management-update-url-value="{{ path('api_user_update', {id: '__ID__'}) }}"
     data-main-selection-delete-url-value="{{ path('api_user_delete_multiple') }}"
     data-main-selection-refresh-url-value="{{ path('api_user_index') }}"
     data-modal-selection-load-users-url-value="{{ path('api_ldap_sync_available_users') }}"
     data-modal-selection-add-users-url-value="{{ path('api_ldap_sync_add_users') }}"
     data-action="modal-selection:usersAdded->main-selection#refreshTable user-management:syncCompleted->main-selection#refreshTable user-management:userUpdated->main-selection#refreshTable">

    <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
            <h1 class="text-2xl font-semibold text-gray-900">Utilisateurs</h1>
            <p class="mt-2 text-sm text-gray-700">Liste de tous les utilisateurs actifs</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-3">
            <button type="button"
                    data-action="click->modal-selection#open"
                    class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700">
                Ajouter des utilisateurs
            </button>
            <button type="button"
                    data-action="click->user-management#syncLdapUsers"
                    class="inline-flex items-center justify-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700">
                Synchroniser LDAP
            </button>
            <button type="button"
                    data-action="click->main-selection#deleteSelected"
                    data-main-selection-target="deleteBtn"
                    class="inline-flex items-center justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 hidden">
                Supprimer la sélection
            </button>
        </div>
    </div>

    <!-- Indicateur de sélection -->
    <div data-main-selection-target="indicator" class="mt-4 bg-blue-50 border border-blue-200 rounded-md p-3 hidden">
        <div class="flex items-center justify-between">
            <span data-main-selection-target="selectedCount" class="text-sm text-blue-800">0 utilisateur(s) sélectionné(s)</span>
            <div class="space-x-2">
                <button type="button"
                        data-action="click->main-selection#selectAll"
                        class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                    Tout sélectionner
                </button>
                <button type="button"
                        data-action="click->main-selection#clearSelection"
                        class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded hover:bg-gray-200">
                    Tout désélectionner
                </button>
            </div>
        </div>
    </div>

    <!-- Bouton flottant de suppression -->
    <div data-main-selection-target="floatingBtn" class="fixed bottom-6 right-6 z-50 hidden">
        <button type="button"
                data-action="click->main-selection#deleteSelected"
                class="inline-flex items-center justify-center rounded-full bg-red-600 px-6 py-3 text-sm font-medium text-white shadow-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            <span data-main-selection-target="floatingCount">Supprimer 0</span>
        </button>
    </div>

    <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôle</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Horaire</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody data-main-selection-target="container" class="bg-white divide-y divide-gray-200 user-selection-container">
                            {% for user in users %}
                                <tr class="user-item cursor-pointer transition-all duration-200 hover:bg-gray-50" data-user-id="{{ user.id }}">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                                    <span class="text-white font-medium">{{ user.prenom|first }}{{ user.nom|first }}</span>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900">{{ user.nomComplet }}</div>
                                                <div class="text-sm text-gray-500">{{ user.telephone ?? 'N/A' }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.email }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ user.roleDisplay }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {% if user.horaireHebdo %}{{ user.horaireHebdo }}h{% else %}Non défini{% endif %}
                                        {% if user.forfaitJour %}
                                            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Forfait jour
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if user.actif %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Actif
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Inactif
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <a href="{{ path('app_user_detail', {id: user.id}) }}" class="text-blue-600 hover:text-blue-900">Voir</a>
                                            <button data-action="click->user-management#editUser"
                                                    data-user-id="{{ user.id }}"
                                                    class="text-indigo-600 hover:text-indigo-900">Modifier</button>
                                            <button data-action="click->user-management#deleteUser"
                                                    data-user-id="{{ user.id }}"
                                                    data-user-name="{{ user.nomComplet }}"
                                                    class="text-red-600 hover:text-red-900">Supprimer</button>
                                        </div>
                                    </td>
                                </tr>
                            {% else %}
                                <tr>
                                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">Aucun utilisateur trouvé</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'ajout d'utilisateurs (sélection multiple) -->
    <div data-modal-selection-target="modal"
         data-action="click->modal-selection#closeOnBackdrop"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Ajouter des utilisateurs à OSI</h3>
                    <button type="button"
                            data-action="click->modal-selection#close"
                            class="text-gray-400 hover:text-gray-600">
                        <span class="sr-only">Fermer</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div class="mb-4">
                    <input type="text"
                           data-modal-selection-target="searchInput"
                           placeholder="Rechercher un utilisateur..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="mb-4 flex justify-between items-center">
                    <span data-modal-selection-target="selectedCount" class="text-sm text-gray-600">0 utilisateur(s) sélectionné(s)</span>
                    <div class="space-x-2">
                        <button type="button"
                                data-action="click->modal-selection#selectAll"
                                class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                            Tout sélectionner
                        </button>
                        <button type="button"
                                data-action="click->modal-selection#clearSelection"
                                class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded hover:bg-gray-200">
                            Tout désélectionner
                        </button>
                    </div>
                </div>

                <div data-modal-selection-target="container" class="border border-gray-200 rounded-md max-h-96 overflow-y-auto user-selection-container">
                    <!-- Les utilisateurs seront chargés ici -->
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button"
                            data-action="click->modal-selection#close"
                            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Annuler
                    </button>
                    <button type="button"
                            data-action="click->modal-selection#addSelectedUsers"
                            class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700">
                        Ajouter les utilisateurs sélectionnés
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'édition d'utilisateur -->
    <div data-user-management-target="editModal"
         data-action="click->user-management#closeEditModalOnBackdrop"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Modifier l'utilisateur</h3>
                    <button type="button"
                            data-action="click->user-management#closeEditModal"
                            class="text-gray-400 hover:text-gray-600">
                        <span class="sr-only">Fermer</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <form data-user-management-target="editForm">
                    <input type="hidden" id="editUserId" name="userId">

                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="editNom" class="block text-sm font-medium text-gray-700">Nom</label>
                            <input type="text" id="editNom" name="nom" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editPrenom" class="block text-sm font-medium text-gray-700">Prénom</label>
                            <input type="text" id="editPrenom" name="prenom" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editEmail" class="block text-sm font-medium text-gray-700">Email</label>
                            <input type="email" id="editEmail" name="email" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editTelephone" class="block text-sm font-medium text-gray-700">Téléphone</label>
                            <input type="text" id="editTelephone" name="telephone" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editHoraireHebdo" class="block text-sm font-medium text-gray-700">Horaire hebdomadaire</label>
                            <input type="number" id="editHoraireHebdo" name="horaireHebdo" min="1" max="60" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editDateEmbauche" class="block text-sm font-medium text-gray-700">Date d'embauche</label>
                            <input type="date" id="editDateEmbauche" name="dateEmbauche" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editDateDepart" class="block text-sm font-medium text-gray-700">Date de départ</label>
                            <input type="date" id="editDateDepart" name="dateDepart" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editSecteur" class="block text-sm font-medium text-gray-700">Secteur</label>
                            <input type="text" id="editSecteur" name="secteur" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editUsername" class="block text-sm font-medium text-gray-700">Username</label>
                            <input type="text" id="editUsername" name="username" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editManager" class="block text-sm font-medium text-gray-700">Manager</label>
                            <input type="text" id="editManager" name="manager" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editTitre" class="block text-sm font-medium text-gray-700">Titre</label>
                            <input type="text" id="editTitre" name="titre" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="editMobile" class="block text-sm font-medium text-gray-700">Mobile</label>
                            <input type="text" id="editMobile" name="mobile" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="editForfaitJour" name="forfaitJour" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="editForfaitJour" class="ml-2 block text-sm text-gray-900">Forfait jour</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="editActif" name="actif" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="editActif" class="ml-2 block text-sm text-gray-900">Actif</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="editIsManager" name="isManager" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="editIsManager" class="ml-2 block text-sm text-gray-900">Manager</label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" id="editVpn" name="vpn" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="editVpn" class="ml-2 block text-sm text-gray-900">VPN</label>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button"
                                data-action="click->user-management#closeEditModal"
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Annuler
                        </button>
                        <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            Modifier
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
{% endblock %}