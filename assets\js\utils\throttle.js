/**
 * Utilitaires de throttling et debouncing
 */

/**
 * Throttle une fonction pour qu'elle ne s'exécute pas plus d'une fois par intervalle
 * @param {Function} func - Fonction à throttler
 * @param {number} delay - <PERSON><PERSON><PERSON> en millisecondes
 * @returns {Function} Fonction throttlée
 */
export function throttle(func, delay) {
    let timeoutId;
    let lastExecTime = 0;
    
    return function (...args) {
        const currentTime = Date.now();
        
        if (currentTime - lastExecTime > delay) {
            func.apply(this, args);
            lastExecTime = currentTime;
        } else {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
                func.apply(this, args);
                lastExecTime = Date.now();
            }, delay - (currentTime - lastExecTime));
        }
    };
}

/**
 * Debounce une fonction pour qu'elle ne s'exécute qu'après un délai d'inactivité
 * @param {Function} func - Fonction à debouncer
 * @param {number} delay - <PERSON><PERSON><PERSON> en millisecondes
 * @returns {Function} Fonction debouncée
 */
export function debounce(func, delay) {
    let timeoutId;
    
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

/**
 * Throttle optimisé pour les événements de mouvement de souris
 * Utilise requestAnimationFrame pour de meilleures performances
 * @param {Function} func - Fonction à throttler
 * @returns {Function} Fonction throttlée
 */
export function throttleAnimationFrame(func) {
    let animationFrameId = null;
    
    return function (...args) {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
        }
        
        animationFrameId = requestAnimationFrame(() => {
            func.apply(this, args);
            animationFrameId = null;
        });
    };
}
