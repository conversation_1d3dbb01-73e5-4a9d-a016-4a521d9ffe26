<?php

namespace App\Entity;

use App\Repository\PrimeRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: PrimeRepository::class)]
class Prime
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['prime:read'])]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'primes')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?User $user = null;

    #[ORM\ManyToOne(inversedBy: 'primes')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?Mission $mission = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    #[Assert\NotNull]
    #[Assert\PositiveOrZero]
    #[Groups(['prime:read', 'prime:write'])]
    private ?string $montant = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Assert\NotNull]
    #[Groups(['prime:read', 'prime:write'])]
    private ?\DateTimeInterface $periodeDebut = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Assert\NotNull]
    #[Groups(['prime:read', 'prime:write'])]
    private ?\DateTimeInterface $periodeFin = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?bool $validationResponsableOsi = false;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?\DateTimeInterface $dateValidationResponsableOsi = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?User $valideParResponsableOsi = null;

    #[ORM\Column(nullable: true)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?bool $validationAssistanteRh = false;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?\DateTimeInterface $dateValidationAssistanteRh = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    #[Groups(['prime:read', 'prime:write'])]
    private ?User $valideParAssistanteRh = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['prime:read'])]
    private ?\DateTimeInterface $dateCreation = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    #[Groups(['prime:read'])]
    private ?\DateTimeInterface $dateModification = null;

    public function __construct()
    {
        $this->dateCreation = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;
        return $this;
    }

    public function getMission(): ?Mission
    {
        return $this->mission;
    }

    public function setMission(?Mission $mission): static
    {
        $this->mission = $mission;
        return $this;
    }

    public function getMontant(): ?string
    {
        return $this->montant;
    }

    public function setMontant(string $montant): static
    {
        $this->montant = $montant;
        return $this;
    }

    public function getMontantFloat(): float
    {
        return (float) $this->montant;
    }

    public function setMontantFloat(float $montant): static
    {
        $this->montant = (string) $montant;
        return $this;
    }

    public function getPeriodeDebut(): ?\DateTimeInterface
    {
        return $this->periodeDebut;
    }

    public function setPeriodeDebut(\DateTimeInterface $periodeDebut): static
    {
        $this->periodeDebut = $periodeDebut;
        return $this;
    }

    public function getPeriodeFin(): ?\DateTimeInterface
    {
        return $this->periodeFin;
    }

    public function setPeriodeFin(\DateTimeInterface $periodeFin): static
    {
        $this->periodeFin = $periodeFin;
        return $this;
    }

    public function isValidationResponsableOsi(): ?bool
    {
        return $this->validationResponsableOsi;
    }

    public function setValidationResponsableOsi(?bool $validationResponsableOsi): static
    {
        $this->validationResponsableOsi = $validationResponsableOsi;
        return $this;
    }

    public function getDateValidationResponsableOsi(): ?\DateTimeInterface
    {
        return $this->dateValidationResponsableOsi;
    }

    public function setDateValidationResponsableOsi(?\DateTimeInterface $dateValidationResponsableOsi): static
    {
        $this->dateValidationResponsableOsi = $dateValidationResponsableOsi;
        return $this;
    }

    public function getValideParResponsableOsi(): ?User
    {
        return $this->valideParResponsableOsi;
    }

    public function setValideParResponsableOsi(?User $valideParResponsableOsi): static
    {
        $this->valideParResponsableOsi = $valideParResponsableOsi;
        return $this;
    }

    public function isValidationAssistanteRh(): ?bool
    {
        return $this->validationAssistanteRh;
    }

    public function setValidationAssistanteRh(?bool $validationAssistanteRh): static
    {
        $this->validationAssistanteRh = $validationAssistanteRh;
        return $this;
    }

    public function getDateValidationAssistanteRh(): ?\DateTimeInterface
    {
        return $this->dateValidationAssistanteRh;
    }

    public function setDateValidationAssistanteRh(?\DateTimeInterface $dateValidationAssistanteRh): static
    {
        $this->dateValidationAssistanteRh = $dateValidationAssistanteRh;
        return $this;
    }

    public function getValideParAssistanteRh(): ?User
    {
        return $this->valideParAssistanteRh;
    }

    public function setValideParAssistanteRh(?User $valideParAssistanteRh): static
    {
        $this->valideParAssistanteRh = $valideParAssistanteRh;
        return $this;
    }

    public function getDateCreation(): ?\DateTimeInterface
    {
        return $this->dateCreation;
    }

    public function setDateCreation(\DateTimeInterface $dateCreation): static
    {
        $this->dateCreation = $dateCreation;
        return $this;
    }

    public function getDateModification(): ?\DateTimeInterface
    {
        return $this->dateModification;
    }

    public function setDateModification(?\DateTimeInterface $dateModification): static
    {
        $this->dateModification = $dateModification;
        return $this;
    }

    // Méthodes de validation
    public function validerParResponsableOsi(User $validateur): static
    {
        $this->validationResponsableOsi = true;
        $this->dateValidationResponsableOsi = new \DateTime();
        $this->valideParResponsableOsi = $validateur;
        $this->dateModification = new \DateTime();
        return $this;
    }

    public function invaliderParResponsableOsi(): static
    {
        $this->validationResponsableOsi = false;
        $this->dateValidationResponsableOsi = null;
        $this->valideParResponsableOsi = null;
        $this->dateModification = new \DateTime();
        return $this;
    }

    public function validerParAssistanteRh(User $validateur): static
    {
        $this->validationAssistanteRh = true;
        $this->dateValidationAssistanteRh = new \DateTime();
        $this->valideParAssistanteRh = $validateur;
        $this->dateModification = new \DateTime();
        return $this;
    }

    public function invaliderParAssistanteRh(): static
    {
        $this->validationAssistanteRh = false;
        $this->dateValidationAssistanteRh = null;
        $this->valideParAssistanteRh = null;
        $this->dateModification = new \DateTime();
        return $this;
    }

    public function isCompletelyValidated(): bool
    {
        return $this->validationResponsableOsi && $this->validationAssistanteRh;
    }

    public function canBeValidatedByRh(): bool
    {
        return $this->validationResponsableOsi && !$this->validationAssistanteRh;
    }

    public function getStatutValidation(): string
    {
        if ($this->isCompletelyValidated()) {
            return 'Validée';
        }
        if ($this->validationResponsableOsi) {
            return 'En attente validation RH';
        }
        return 'En attente validation responsable';
    }
}
