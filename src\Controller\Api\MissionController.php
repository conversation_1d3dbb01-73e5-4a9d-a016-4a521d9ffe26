<?php

namespace App\Controller\Api;

use App\Entity\Mission;
use App\Entity\User;
use App\Entity\Client;
use App\Entity\Site;
use App\Repository\MissionRepository;
use App\Repository\UserRepository;
use App\Repository\ClientRepository;
use App\Repository\SiteRepository;


use App\Service\PrimeCalculatorService;
use App\Service\ValidationService;
use App\Service\HeureHebdomadaireService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/missions', name: 'api_mission_')]
class MissionController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private MissionRepository $missionRepository,
        private UserRepository $userRepository,
        private ClientRepository $clientRepository,
        private SiteRepository $siteRepository,


        private PrimeCalculatorService $primeCalculator,
        private ValidationService $validationService,
        private HeureHebdomadaireService $heureHebdomadaireService
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $zone = $request->query->get('zone');
        $pays = $request->query->get('pays');
        $niveau = $request->query->get('niveau');
        $userId = $request->query->get('user');
        $debut = $request->query->get('debut');
        $fin = $request->query->get('fin');

        // Construire les critères de recherche
        $criteria = [];
        if ($zone) $criteria['zone'] = $zone;
        if ($pays) $criteria['pays'] = $pays;
        if ($niveau) $criteria['niveau'] = (int) $niveau;
        $user = null;
        if ($userId) {
            $user = $this->userRepository->find($userId);
        }
        if ($debut && $fin) {
            $criteria['dateDebut'] = new \DateTime($debut);
            $criteria['dateFin'] = new \DateTime($fin);
        }

        // Utiliser la nouvelle méthode pour chercher par utilisateur
        if ($user) {
            $missions = $this->missionRepository->findByUserAndCriteria($user, $criteria);
        } else {
            $missions = $this->missionRepository->findWithCriteria($criteria);
        }

        return $this->json($missions, Response::HTTP_OK, [], [
            'groups' => ['mission:read', 'mission:users'],
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            }
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(Mission $mission): JsonResponse
    {
        return $this->json($mission, Response::HTTP_OK, [], [
            'groups' => ['mission:read', 'mission:detail', 'mission:users', 'client:read', 'site:read']
        ]);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        // Vérifier qu'au moins un utilisateur est fourni
        if (empty($data['userIds']) || !is_array($data['userIds'])) {
            return $this->json(['error' => 'Au moins un utilisateur doit être assigné'], Response::HTTP_BAD_REQUEST);
        }

        // Vérifier qu'un client est fourni
        if (empty($data['clientId'])) {
            return $this->json(['error' => 'Un client doit être sélectionné'], Response::HTTP_BAD_REQUEST);
        }

        // Récupérer le client
        $client = $this->clientRepository->find($data['clientId']);
        if (!$client) {
            return $this->json(['error' => 'Client non trouvé'], Response::HTTP_BAD_REQUEST);
        }

        // Récupérer le site (optionnel)
        $site = null;
        if (!empty($data['siteId'])) {
            $site = $this->siteRepository->find($data['siteId']);
            if (!$site) {
                return $this->json(['error' => 'Site non trouvé'], Response::HTTP_BAD_REQUEST);
            }
        }

        // Récupérer les utilisateurs
        $users = [];
        foreach ($data['userIds'] as $userId) {
            $user = $this->userRepository->find($userId);
            if (!$user) {
                return $this->json(['error' => "Utilisateur avec l'ID $userId non trouvé"], Response::HTTP_BAD_REQUEST);
            }
            $users[] = $user;
        }

        $mission = new Mission();
        $mission->setTitre($data['titre'] ?? '')
                ->setPays($data['pays'] ?? '')
                ->setDateDebut(new \DateTime($data['dateDebut'] ?? 'now'))
                ->setDateFin(new \DateTime($data['dateFin'] ?? 'now'))
                ->setNiveau($data['niveau'] ?? 1)
                ->setZone($data['zone'] ?? 'EURO')
                ->setClient($client);

        if ($site) {
            $mission->setSite($site);
        }

        // Ajouter les utilisateurs à la mission
        foreach ($users as $user) {
            $mission->addUser($user);
        }

        $errors = $this->validator->validate($mission);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->persist($mission);
        $this->entityManager->flush();

        return $this->json($mission, Response::HTTP_CREATED, [], [
            'groups' => ['mission:read', 'user:read', 'client:read', 'site:read']
        ]);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT'])]
    public function update(Request $request, Mission $mission): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (isset($data['titre'])) $mission->setTitre($data['titre']);
        if (isset($data['pays'])) $mission->setPays($data['pays']);
        if (isset($data['dateDebut'])) $mission->setDateDebut(new \DateTime($data['dateDebut']));
        if (isset($data['dateFin'])) $mission->setDateFin(new \DateTime($data['dateFin']));
        if (isset($data['niveau'])) $mission->setNiveau($data['niveau']);
        if (isset($data['zone'])) $mission->setZone($data['zone']);

        // Gestion du client
        if (isset($data['clientId'])) {
            $client = $this->clientRepository->find($data['clientId']);
            if (!$client) {
                return $this->json(['error' => 'Client non trouvé'], Response::HTTP_BAD_REQUEST);
            }
            $mission->setClient($client);
        }

        // Gestion du site
        if (isset($data['siteId'])) {
            if ($data['siteId']) {
                $site = $this->siteRepository->find($data['siteId']);
                if (!$site) {
                    return $this->json(['error' => 'Site non trouvé'], Response::HTTP_BAD_REQUEST);
                }
                $mission->setSite($site);
            } else {
                $mission->setSite(null);
            }
        }

        // Gestion des utilisateurs
        if (isset($data['userIds']) && is_array($data['userIds'])) {
            // Supprimer tous les utilisateurs actuels
            foreach ($mission->getUsers() as $user) {
                $mission->removeUser($user);
            }

            // Ajouter les nouveaux utilisateurs
            foreach ($data['userIds'] as $userId) {
                $user = $this->userRepository->find($userId);
                if (!$user) {
                    return $this->json(['error' => "Utilisateur avec l'ID $userId non trouvé"], Response::HTTP_BAD_REQUEST);
                }
                $mission->addUser($user);
            }
        }

        $errors = $this->validator->validate($mission);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->flush();

        return $this->json($mission, Response::HTTP_OK, [], [
            'groups' => ['mission:read', 'mission:users', 'client:read', 'site:read']
        ]);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(Mission $mission): JsonResponse
    {
        $this->entityManager->remove($mission);
        $this->entityManager->flush();

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    #[Route('/statistiques', name: 'statistiques', methods: ['GET'])]
    public function statistiques(): JsonResponse
    {
        $stats = $this->missionRepository->getStatistiques();
        return $this->json($stats);
    }

    #[Route('/{id}/primes', name: 'primes', methods: ['GET'])]
    public function primes(Mission $mission): JsonResponse
    {
        $primesTotales = $this->primeCalculator->calculPrimesTotalesMission($mission);

        // Calculer la durée réelle de la mission
        $dureeJours = $mission->getDateDebut() && $mission->getDateFin()
            ? $mission->getDateDebut()->diff($mission->getDateFin())->days + 1
            : 0;

        // Créer manuellement les données des primes par utilisateur pour éviter les références circulaires
        $primesParUtilisateur = [];
        $users = $mission->getUsers();
        $nombreUsers = count($users);

        if ($nombreUsers > 0) {
            $primeParUser = $primesTotales / $nombreUsers;

            foreach ($users as $user) {
                // Calculer les heures réelles travaillées pendant la mission
                $heuresReelles = 0;
                if ($mission->getDateDebut() && $mission->getDateFin()) {
                    $calculPeriode = $this->heureHebdomadaireService->calculerHeuresPeriode(
                        $user,
                        $mission->getDateDebut(),
                        $mission->getDateFin()
                    );
                    $heuresReelles = $calculPeriode['totaux']['heuresSaisies'];
                }

                $primesParUtilisateur[] = [
                    'user' => [
                        'id' => $user->getId(),
                        'nom' => $user->getNom(),
                        'prenom' => $user->getPrenom(),
                        'role' => $user->getRoleDisplay()
                    ],
                    'prime' => $primeParUser,
                    'heuresReelles' => $heuresReelles
                ];
            }
        }

        return $this->json([
            'mission' => $mission->getTitre(),
            'primesTotales' => $primesTotales,
            'primesParUtilisateur' => $primesParUtilisateur,
            'zone' => $mission->getZone(),
            'niveau' => $mission->getNiveau(),
            'dureeJours' => $dureeJours,
            'dateDebut' => $mission->getDateDebut()?->format('Y-m-d'),
            'dateFin' => $mission->getDateFin()?->format('Y-m-d')
        ]);
    }

    #[Route('/{id}/segments', name: 'segments', methods: ['GET'])]
    public function segments(Mission $mission): JsonResponse
    {
        $segments = $mission->getSegments();

        return $this->json($segments, Response::HTTP_OK, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/en-cours', name: 'en_cours', methods: ['GET'])]
    public function enCours(): JsonResponse
    {
        $missions = $this->missionRepository->findEnCours();

        return $this->json($missions, Response::HTTP_OK, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/prochaines', name: 'prochaines', methods: ['GET'])]
    public function prochaines(Request $request): JsonResponse
    {
        $limit = (int) $request->query->get('limit', 10);
        $missions = $this->missionRepository->findProchaines($limit);

        return $this->json($missions, Response::HTTP_OK, [], [
            'groups' => ['mission:read']
        ]);
    }

    #[Route('/{id}/validation', name: 'validation', methods: ['GET'])]
    public function validation(Mission $mission): JsonResponse
    {
        $validation = $this->validationService->validerMission($mission);

        return $this->json($validation);
    }
}
