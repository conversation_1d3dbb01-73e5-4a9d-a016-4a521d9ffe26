<?php

namespace App\Controller;

use App\Entity\User;
use App\Entity\Mission;
use App\Entity\Segment;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class TestDataController extends AbstractController
{
    #[Route('/load-test-data', name: 'load_test_data')]
    public function loadTestData(EntityManagerInterface $entityManager): Response
    {
        // Créer des utilisateurs
        $user1 = new User();
        $user1->setNom('Dupont')
              ->setPrenom('Jean')
              ->setEmail('<EMAIL>')
              ->setRoles(['ROLE_MANAGER'])
              ->setHoraireHebdo(35)
              ->setForfaitJour(false);
        $entityManager->persist($user1);

        $user2 = new User();
        $user2->setNom('<PERSON>')
              ->setPrenom('Marie')
              ->setEmail('<EMAIL>')
              ->setRoles(['ROLE_USER'])
              ->setHoraireHebdo(35)
              ->setForfaitJour(true);
        $entityManager->persist($user2);

        $user3 = new User();
        $user3->setNom('Bernard')
              ->setPrenom('Pierre')
              ->setEmail('<EMAIL>')
              ->setRoles(['ROLE_USER'])
              ->setHoraireHebdo(39)
              ->setForfaitJour(false);
        $entityManager->persist($user3);

        // Créer des missions
        $mission1 = new Mission();
        $mission1->addUser($user1)
                 ->setTitre('Audit sécurité - Banque Centrale')
                 ->setPays('France')
                 ->setDateDebut(new \DateTime('2024-01-15'))
                 ->setDateFin(new \DateTime('2024-01-25'))
                 ->setNiveau(Mission::NIVEAU_2)
                 ->setZone(Mission::ZONE_EURO);
        $entityManager->persist($mission1);

        $mission2 = new Mission();
        $mission2->addUser($user2)
                 ->setTitre('Formation cybersécurité')
                 ->setPays('Allemagne')
                 ->setDateDebut(new \DateTime('2024-02-01'))
                 ->setDateFin(new \DateTime('2024-02-05'))
                 ->setNiveau(Mission::NIVEAU_1)
                 ->setZone(Mission::ZONE_EURO);
        $entityManager->persist($mission2);

        $mission3 = new Mission();
        $mission3->addUser($user3)
                 ->setTitre('Implémentation système - Tokyo')
                 ->setPays('Japon')
                 ->setDateDebut(new \DateTime('2024-02-10'))
                 ->setDateFin(new \DateTime('2024-02-20'))
                 ->setNiveau(Mission::NIVEAU_2)
                 ->setZone(Mission::ZONE_HORS_EURO);
        $entityManager->persist($mission3);

        // Créer des segments
        $segment1 = new Segment();
        $segment1->setMission($mission1)
                 ->setType(Segment::TYPE_VOYAGE)
                 ->setDateHeureDebut(new \DateTime('2024-01-15 06:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-15 10:00'));
        $entityManager->persist($segment1);

        $segment2 = new Segment();
        $segment2->setMission($mission1)
                 ->setType(Segment::TYPE_INTERVENTION)
                 ->setDateHeureDebut(new \DateTime('2024-01-15 14:00'))
                 ->setDateHeureFin(new \DateTime('2024-01-15 18:00'));
        $entityManager->persist($segment2);

        $segment3 = new Segment();
        $segment3->setMission($mission2)
                 ->setType(Segment::TYPE_VOYAGE)
                 ->setDateHeureDebut(new \DateTime('2024-02-01 07:00'))
                 ->setDateHeureFin(new \DateTime('2024-02-01 09:00'));
        $entityManager->persist($segment3);

        // Les heures sont calculées automatiquement depuis les segments

        $entityManager->flush();

        return new Response('Données de test chargées avec succès !');
    }
}
