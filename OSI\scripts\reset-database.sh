#!/bin/bash

# Script pour réinitialiser la base de données MySQL
echo "🔄 Réinitialisation de la base de données OSI Manager..."

# Supprimer la base de données
echo "📥 Suppression de la base de données..."
php bin/console doctrine:database:drop --force --if-exists

# Créer la base de données
echo "📤 Création de la base de données..."
php bin/console doctrine:database:create

# Créer le schéma
echo "🏗️ Création du schéma..."
php bin/console doctrine:schema:create

# Charger les données de test
echo "📊 Chargement des données de test..."
curl -s http://localhost:8000/load-test-data

echo "✅ Base de données réinitialisée avec succès !"
echo "🌐 Accédez à l'application : http://localhost:8000"
