<?php

namespace App\Controller\Api;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/users', name: 'api_user_')]
class UserController extends AbstractController
{
    public function __construct(
        private UserRepository $userRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $search = $request->query->get('search');

        if ($search) {
            $users = $this->userRepository->findByNomPrenom($search);
        } else {
            $users = $this->userRepository->findActifs();
        }

        return $this->json([
            'data' => $users,
            'total' => count($users)
        ], Response::HTTP_OK, [], [
            'groups' => ['user:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(User $user): JsonResponse
    {
        return $this->json($user, Response::HTTP_OK, [], [
            'groups' => ['user:read', 'user:detail']
        ]);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $user = new User();
        $user->setNom($data['nom'] ?? '')
            ->setPrenom($data['prenom'] ?? '')
            ->setEmail($data['email'] ?? '')
            ->setHoraireHebdo($data['horaireHebdo'] ?? null)
            ->setForfaitJour($data['forfaitJour'] ?? false)
            ->setTelephone($data['telephone'] ?? null)
            ->setActif(true);

        // Nouveaux champs
        if (isset($data['secteur'])) $user->setSecteur($data['secteur']);
        if (isset($data['username'])) $user->setUsername($data['username']);
        if (isset($data['manager'])) $user->setManager($data['manager']);
        if (isset($data['titre'])) $user->setTitre($data['titre']);
        if (isset($data['isManager'])) $user->setIsManager($data['isManager']);
        if (isset($data['vpn'])) $user->setVpn($data['vpn']);
        if (isset($data['mobile'])) $user->setMobile($data['mobile']);
        if (isset($data['roles'])) $user->setRoles($data['roles']);

        if (isset($data['dateEmbauche'])) {
            $user->setDateEmbauche(new \DateTime($data['dateEmbauche']));
        }

        $this->userRepository->save($user, true);

        return $this->json($user, Response::HTTP_CREATED, [], [
            'groups' => ['user:read']
        ]);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT', 'PATCH'])]
    public function update(Request $request, User $user): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (isset($data['nom'])) $user->setNom($data['nom']);
        if (isset($data['prenom'])) $user->setPrenom($data['prenom']);
        if (isset($data['email'])) $user->setEmail($data['email']);
        if (isset($data['horaireHebdo'])) $user->setHoraireHebdo($data['horaireHebdo']);
        if (isset($data['forfaitJour'])) $user->setForfaitJour($data['forfaitJour']);
        if (isset($data['telephone'])) $user->setTelephone($data['telephone']);
        if (isset($data['actif'])) $user->setActif($data['actif']);

        // Nouveaux champs
        if (isset($data['secteur'])) $user->setSecteur($data['secteur']);
        if (isset($data['username'])) $user->setUsername($data['username']);
        if (isset($data['manager'])) $user->setManager($data['manager']);
        if (isset($data['titre'])) $user->setTitre($data['titre']);
        if (isset($data['isManager'])) $user->setIsManager($data['isManager']);
        if (isset($data['vpn'])) $user->setVpn($data['vpn']);
        if (isset($data['mobile'])) $user->setMobile($data['mobile']);
        if (isset($data['roles'])) $user->setRoles($data['roles']);

        if (isset($data['dateEmbauche'])) {
            $user->setDateEmbauche(new \DateTime($data['dateEmbauche']));
        }

        if (isset($data['dateDepart'])) {
            $user->setDateDepart(new \DateTime($data['dateDepart']));
        }

        $this->userRepository->save($user, true);

        return $this->json($user, Response::HTTP_OK, [], [
            'groups' => ['user:read']
        ]);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(User $user): JsonResponse
    {
        // Au lieu de supprimer, on passe userOsi à false
        $user->setUserOsi(false);
        $this->userRepository->save($user, true);

        return $this->json([
            'success' => true,
            'message' => 'Utilisateur retiré d\'OSI',
            'userId' => $user->getId()
        ], Response::HTTP_OK);
    }

    #[Route('/delete-multiple', name: 'delete_multiple', methods: ['POST'])]
    public function deleteMultiple(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $userIds = $data['userIds'] ?? [];

        if (empty($userIds)) {
            return $this->json(['success' => false, 'message' => 'Aucun utilisateur sélectionné'], 400);
        }

        try {
            $count = 0;
            foreach ($userIds as $userId) {
                $user = $this->userRepository->find($userId);
                if ($user) {
                    $user->setUserOsi(false);
                    $count++;
                }
            }

            $this->entityManager->flush();

            return $this->json([
                'success' => true,
                'message' => "{$count} utilisateur(s) retiré(s) d'OSI",
                'count' => $count
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur lors du retrait des utilisateurs : ' . $e->getMessage()
            ], 500);
        }
    }
}
