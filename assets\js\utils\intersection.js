/**
 * Utilitaires pour les calculs d'intersection géométrique
 */

/**
 * Vérifie si deux rectangles se chevauchent
 * @param {Object} rect1 - Premier rectangle {left, top, right, bottom}
 * @param {Object} rect2 - Deuxième rectangle {left, top, right, bottom}
 * @returns {boolean} True si les rectangles se chevauchent
 */
export function rectanglesIntersect(rect1, rect2) {
    return !(rect1.right < rect2.left || 
             rect1.left > rect2.right ||
             rect1.bottom < rect2.top || 
             rect1.top > rect2.bottom);
}

/**
 * Calcule l'intersection entre une zone de sélection et un élément DOM
 * @param {Object} selectionBox - Zone de sélection {left, top, width, height}
 * @param {HTMLElement} element - Élément DOM
 * @param {HTMLElement} container - Conteneur de référence (optionnel)
 * @returns {boolean} True si l'élément intersecte avec la zone de sélection
 */
export function elementIntersectsSelection(selectionBox, element, container = null) {
    const rect = element.getBoundingClientRect();
    
    let elementBounds;
    
    if (container) {
        // Coordonnées relatives au conteneur
        const containerRect = container.getBoundingClientRect();
        elementBounds = {
            left: rect.left - containerRect.left + container.scrollLeft,
            top: rect.top - containerRect.top + container.scrollTop,
            right: rect.right - containerRect.left + container.scrollLeft,
            bottom: rect.bottom - containerRect.top + container.scrollTop
        };
    } else {
        // Coordonnées absolues de la page
        const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
        const scrollY = window.pageYOffset || document.documentElement.scrollTop;
        
        elementBounds = {
            left: rect.left + scrollX,
            top: rect.top + scrollY,
            right: rect.right + scrollX,
            bottom: rect.bottom + scrollY
        };
    }
    
    const selectionBounds = {
        left: selectionBox.left,
        top: selectionBox.top,
        right: selectionBox.left + selectionBox.width,
        bottom: selectionBox.top + selectionBox.height
    };
    
    return rectanglesIntersect(elementBounds, selectionBounds);
}

/**
 * Convertit les coordonnées viewport en coordonnées absolues de page
 * @param {number} clientX - Coordonnée X du viewport
 * @param {number} clientY - Coordonnée Y du viewport
 * @returns {Object} Coordonnées absolues {x, y}
 */
export function viewportToPageCoordinates(clientX, clientY) {
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;
    
    return {
        x: clientX + scrollX,
        y: clientY + scrollY
    };
}

/**
 * Convertit les coordonnées viewport en coordonnées relatives à un conteneur
 * @param {number} clientX - Coordonnée X du viewport
 * @param {number} clientY - Coordonnée Y du viewport
 * @param {HTMLElement} container - Conteneur de référence
 * @returns {Object} Coordonnées relatives {x, y}
 */
export function viewportToContainerCoordinates(clientX, clientY, container) {
    const containerRect = container.getBoundingClientRect();
    
    return {
        x: clientX - containerRect.left + container.scrollLeft,
        y: clientY - containerRect.top + container.scrollTop
    };
}
