<?php

namespace App\Repository;

use App\Entity\Site;
use App\Entity\Client;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Site>
 */
class SiteRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Site::class);
    }

    /**
     * Trouve les sites actifs
     */
    public function findActifs(): array
    {
        return $this->createQueryBuilder('s')
            ->join('s.client', 'c')
            ->andWhere('s.actif = :actif')
            ->andWhere('c.actif = :actif')
            ->setParameter('actif', true)
            ->orderBy('c.nom', 'ASC')
            ->addOrderBy('s.nom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les sites d'un client
     */
    public function findByClient(Client $client): array
    {
        return $this->createQueryBuilder('s')
            ->andWhere('s.client = :client')
            ->andWhere('s.actif = :actif')
            ->setParameter('client', $client)
            ->setParameter('actif', true)
            ->orderBy('s.nom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche de sites par nom
     */
    public function findByNom(string $nom): array
    {
        return $this->createQueryBuilder('s')
            ->join('s.client', 'c')
            ->andWhere('s.nom LIKE :nom OR c.nom LIKE :nom')
            ->setParameter('nom', '%' . $nom . '%')
            ->andWhere('s.actif = :actif')
            ->andWhere('c.actif = :actif')
            ->setParameter('actif', true)
            ->orderBy('c.nom', 'ASC')
            ->addOrderBy('s.nom', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
