<?php

namespace App\Controller\Api;

use App\Entity\Prime;
use App\Entity\Mission;
use App\Entity\User;
use App\Service\PrimeManagementService;
use App\Repository\PrimeRepository;
use App\Repository\MissionRepository;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/api/primes')]
#[IsGranted('ROLE_USER')]
class PrimeApiController extends AbstractController
{
    public function __construct(
        private PrimeManagementService $primeManagementService,
        private PrimeRepository $primeRepository,
        private MissionRepository $missionRepository,
        private UserRepository $userRepository
    ) {}

    #[Route('', name: 'api_primes_list', methods: ['GET'])]
    public function list(Request $request): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $primes = $this->primeManagementService->getPrimesAccessibles($user);
            
            $data = array_map(function(Prime $prime) {
                return [
                    'id' => $prime->getId(),
                    'user' => [
                        'id' => $prime->getUser()->getId(),
                        'nom_complet' => $prime->getUser()->getNomComplet()
                    ],
                    'mission' => [
                        'id' => $prime->getMission()->getId(),
                        'titre' => $prime->getMission()->getTitre()
                    ],
                    'montant' => $prime->getMontantFloat(),
                    'periode_debut' => $prime->getPeriodeDebut()->format('Y-m-d'),
                    'periode_fin' => $prime->getPeriodeFin()->format('Y-m-d'),
                    'validation_responsable_osi' => $prime->isValidationResponsableOsi(),
                    'validation_assistante_rh' => $prime->isValidationAssistanteRh(),
                    'statut' => $prime->getStatutValidation(),
                    'date_creation' => $prime->getDateCreation()->format('Y-m-d H:i:s'),
                    'date_modification' => $prime->getDateModification()?->format('Y-m-d H:i:s')
                ];
            }, $primes);

            return $this->json([
                'success' => true,
                'data' => $data,
                'total' => count($data)
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/mission/{id}', name: 'api_primes_mission', methods: ['GET'])]
    public function primesMission(Mission $mission): JsonResponse
    {
        $user = $this->getUser();
        
        if (!$user->canAccessValidatedPrimes() && !$user->canValidatePrimesResponsable() && !$user->canValidatePrimesRh()) {
            return $this->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        try {
            $primes = $this->primeRepository->findByMission($mission);
            
            $data = array_map(function(Prime $prime) {
                return [
                    'id' => $prime->getId(),
                    'user' => [
                        'id' => $prime->getUser()->getId(),
                        'nom_complet' => $prime->getUser()->getNomComplet()
                    ],
                    'montant' => $prime->getMontantFloat(),
                    'periode_debut' => $prime->getPeriodeDebut()->format('Y-m-d'),
                    'periode_fin' => $prime->getPeriodeFin()->format('Y-m-d'),
                    'statut' => $prime->getStatutValidation()
                ];
            }, $primes);

            return $this->json([
                'success' => true,
                'data' => $data,
                'mission' => [
                    'id' => $mission->getId(),
                    'titre' => $mission->getTitre()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/generer', name: 'api_primes_generer', methods: ['POST'])]
    public function genererPrimes(Request $request): JsonResponse
    {
        $user = $this->getUser();
        
        if (!$user->canValidatePrimesResponsable()) {
            return $this->json([
                'success' => false,
                'message' => 'Accès refusé : seuls les responsables mission peuvent générer des primes'
            ], 403);
        }

        try {
            $data = json_decode($request->getContent(), true);
            
            $debut = new \DateTime($data['debut']);
            $fin = new \DateTime($data['fin']);
            
            if (isset($data['mission_id'])) {
                $mission = $this->missionRepository->find($data['mission_id']);
                if (!$mission) {
                    throw new \InvalidArgumentException('Mission non trouvée');
                }
                $primes = $this->primeManagementService->genererPrimesMission($mission, $debut, $fin);
            } else {
                $primes = $this->primeManagementService->genererPrimesAutomatiques($debut, $fin);
            }

            return $this->json([
                'success' => true,
                'message' => count($primes) . ' primes générées',
                'primes_generees' => count($primes)
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/statistiques', name: 'api_primes_statistiques', methods: ['GET'])]
    public function statistiques(): JsonResponse
    {
        $user = $this->getUser();
        
        try {
            $stats = $this->primeManagementService->getStatistiquesPrimes($user);
            
            return $this->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/periode', name: 'api_primes_periode', methods: ['GET'])]
    public function primesPeriode(Request $request): JsonResponse
    {
        $user = $this->getUser();
        
        if (!$user->canAccessValidatedPrimes()) {
            return $this->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        try {
            $debut = new \DateTime($request->query->get('debut'));
            $fin = new \DateTime($request->query->get('fin'));
            
            $primes = $this->primeRepository->findValidatedPrimesForPeriod($debut, $fin);
            
            $data = [];
            $totalMontant = 0;
            
            foreach ($primes as $prime) {
                $data[] = [
                    'id' => $prime->getId(),
                    'user' => [
                        'id' => $prime->getUser()->getId(),
                        'nom_complet' => $prime->getUser()->getNomComplet()
                    ],
                    'mission' => [
                        'id' => $prime->getMission()->getId(),
                        'titre' => $prime->getMission()->getTitre()
                    ],
                    'montant' => $prime->getMontantFloat(),
                    'periode_debut' => $prime->getPeriodeDebut()->format('Y-m-d'),
                    'periode_fin' => $prime->getPeriodeFin()->format('Y-m-d')
                ];
                $totalMontant += $prime->getMontantFloat();
            }

            return $this->json([
                'success' => true,
                'data' => $data,
                'total_primes' => count($data),
                'montant_total' => $totalMontant,
                'periode' => [
                    'debut' => $debut->format('Y-m-d'),
                    'fin' => $fin->format('Y-m-d')
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    #[Route('/{id}', name: 'api_prime_detail', methods: ['GET'])]
    public function detail(Prime $prime): JsonResponse
    {
        $user = $this->getUser();
        
        // Vérifier les droits d'accès
        if (!$user->canAccessAllData() && 
            !$user->canValidatePrimesResponsable() && 
            !$user->canValidatePrimesRh() && 
            !$user->canAccessValidatedPrimes() &&
            $prime->getUser() !== $user) {
            return $this->json([
                'success' => false,
                'message' => 'Accès refusé'
            ], 403);
        }

        try {
            return $this->json([
                'success' => true,
                'data' => [
                    'id' => $prime->getId(),
                    'user' => [
                        'id' => $prime->getUser()->getId(),
                        'nom_complet' => $prime->getUser()->getNomComplet()
                    ],
                    'mission' => [
                        'id' => $prime->getMission()->getId(),
                        'titre' => $prime->getMission()->getTitre(),
                        'zone' => $prime->getMission()->getZone(),
                        'niveau' => $prime->getMission()->getNiveau()
                    ],
                    'montant' => $prime->getMontantFloat(),
                    'periode_debut' => $prime->getPeriodeDebut()->format('Y-m-d'),
                    'periode_fin' => $prime->getPeriodeFin()->format('Y-m-d'),
                    'validation_responsable_osi' => $prime->isValidationResponsableOsi(),
                    'date_validation_responsable_osi' => $prime->getDateValidationResponsableOsi()?->format('Y-m-d H:i:s'),
                    'valide_par_responsable_osi' => $prime->getValideParResponsableOsi()?->getNomComplet(),
                    'validation_assistante_rh' => $prime->isValidationAssistanteRh(),
                    'date_validation_assistante_rh' => $prime->getDateValidationAssistanteRh()?->format('Y-m-d H:i:s'),
                    'valide_par_assistante_rh' => $prime->getValideParAssistanteRh()?->getNomComplet(),
                    'statut' => $prime->getStatutValidation(),
                    'date_creation' => $prime->getDateCreation()->format('Y-m-d H:i:s'),
                    'date_modification' => $prime->getDateModification()?->format('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
}
