<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250717120738 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Cette migration est obsolète car les tables et contraintes existent déjà
        // Ne rien faire pour éviter les erreurs de duplication
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mission DROP FOREIGN KEY FK_9067F23C19EB6921');
        $this->addSql('ALTER TABLE mission DROP FOREIGN KEY FK_9067F23CF6BD1646');
        $this->addSql('ALTER TABLE segment DROP FOREIGN KEY FK_1881F565A76ED395');
        $this->addSql('ALTER TABLE semaine_travail DROP FOREIGN KEY FK_4104D40BA76ED395');
        $this->addSql('ALTER TABLE mission_user DROP FOREIGN KEY FK_A4D17A46BE6CAE90');
        $this->addSql('ALTER TABLE mission_user DROP FOREIGN KEY FK_A4D17A46A76ED395');
        $this->addSql('ALTER TABLE site DROP FOREIGN KEY FK_694309E419EB6921');
        $this->addSql('DROP TABLE client');
        $this->addSql('DROP TABLE mission_user');
        $this->addSql('DROP TABLE site');
        $this->addSql('DROP TABLE `user`');
        $this->addSql('DROP INDEX IDX_9067F23C19EB6921 ON mission');
        $this->addSql('DROP INDEX IDX_9067F23CF6BD1646 ON mission');
        $this->addSql('ALTER TABLE mission DROP client_id, DROP site_id, CHANGE collaborateur_id collaborateur_id INT NOT NULL');
        $this->addSql('DROP INDEX IDX_1881F565A76ED395 ON segment');
        $this->addSql('ALTER TABLE segment DROP user_id');
        $this->addSql('DROP INDEX IDX_4104D40BA76ED395 ON semaine_travail');
        $this->addSql('ALTER TABLE semaine_travail DROP user_id, CHANGE collaborateur_id collaborateur_id INT NOT NULL');
    }
}
