<?php

namespace App\Controller;

use App\Entity\Mission;
use App\Repository\MissionRepository;
use App\Service\PrimeCalculatorService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class PrimeDetailController extends AbstractController
{
    public function __construct(
        private MissionRepository $missionRepository,
        private PrimeCalculatorService $primeCalculator
    ) {}

    #[Route('/primes/{id}', name: 'app_prime_detail')]
    public function detail(Mission $mission): Response
    {
        $detailPrimes = $this->primeCalculator->calculDetailPrimesMission($mission);
        
        return $this->render('primes/detail.html.twig', [
            'mission' => $mission,
            'detailPrimes' => $detailPrimes
        ]);
    }

    #[Route('/api/primes/{id}/detail', name: 'api_prime_detail', methods: ['GET'])]
    public function apiDetail(Mission $mission): JsonResponse
    {
        $detailPrimes = $this->primeCalculator->calculDetailPrimesMission($mission);
        
        return $this->json($detailPrimes);
    }
}
