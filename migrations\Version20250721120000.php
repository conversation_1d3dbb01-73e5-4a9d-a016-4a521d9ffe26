<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Ajout des champs manquants à l'entité User pour l'intégration complète
 */
final class Version20250721120000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute les champs secteur, username, manager, titre, isManager, vpn, mobile et roles à la table user';
    }

    public function up(Schema $schema): void
    {
        // Ajouter les nouveaux champs à la table user seulement s'ils n'existent pas
        if ($schema->hasTable('user')) {
            $userTable = $schema->getTable('user');

            if (!$userTable->hasColumn('secteur')) {
                $this->addSql('ALTER TABLE `user` ADD secteur VARCHAR(255) DEFAULT NULL');
            }
            if (!$userTable->hasColumn('username')) {
                $this->addSql('ALTER TABLE `user` ADD username VARCHAR(255) DEFAULT NULL');
            }
            if (!$userTable->hasColumn('manager')) {
                $this->addSql('ALTER TABLE `user` ADD manager VARCHAR(255) DEFAULT NULL');
            }
            if (!$userTable->hasColumn('titre')) {
                $this->addSql('ALTER TABLE `user` ADD titre VARCHAR(255) DEFAULT NULL');
            }
            if (!$userTable->hasColumn('is_manager')) {
                $this->addSql('ALTER TABLE `user` ADD is_manager TINYINT(1) DEFAULT NULL');
            }
            if (!$userTable->hasColumn('vpn')) {
                $this->addSql('ALTER TABLE `user` ADD vpn TINYINT(1) DEFAULT NULL');
            }
            if (!$userTable->hasColumn('mobile')) {
                $this->addSql('ALTER TABLE `user` ADD mobile VARCHAR(20) DEFAULT NULL');
            }
            if (!$userTable->hasColumn('roles')) {
                $this->addSql('ALTER TABLE `user` ADD roles JSON NOT NULL');
                // Initialiser le champ roles avec un tableau vide pour les utilisateurs existants
                $this->addSql('UPDATE `user` SET roles = \'[]\' WHERE roles IS NULL');
            }
        }
    }

    public function down(Schema $schema): void
    {
        // Supprimer les champs ajoutés
        $this->addSql('ALTER TABLE `user` DROP secteur');
        $this->addSql('ALTER TABLE `user` DROP username');
        $this->addSql('ALTER TABLE `user` DROP manager');
        $this->addSql('ALTER TABLE `user` DROP titre');
        $this->addSql('ALTER TABLE `user` DROP is_manager');
        $this->addSql('ALTER TABLE `user` DROP vpn');
        $this->addSql('ALTER TABLE `user` DROP mobile');
        $this->addSql('ALTER TABLE `user` DROP roles');
    }
}
