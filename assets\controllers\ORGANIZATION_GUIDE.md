# Guide d'Organisation des Contrôleurs Stimulus

## 🎯 Objectifs de l'organisation

Cette structure en dossiers permet de :
- **Améliorer la maintenabilité** : Code organisé par fonctionnalité
- **Faciliter la collaboration** : Structure claire et prévisible
- **Réduire les conflits** : Séparation des responsabilités
- **Accélérer le développement** : Réutilisation des composants

## 📁 Structure des dossiers

```
assets/controllers/
├── shared/          # Contrôleurs réutilisables
├── dashboard/       # Page d'accueil
├── missions/        # Gestion des missions
├── users/          # Gestion des utilisateurs
├── heures/         # Gestion des heures
├── calendar/       # Calendrier
└── [nouvelle-page]/ # Nouveaux modules
```

## 🔧 Règles d'organisation

### 1. Placement des contrôleurs

**Dans `/shared/`** si le contrôleur :
- ✅ Est utilisé dans plusieurs pages
- ✅ Fournit une fonctionnalité générique
- ✅ N'a pas de logique métier spécifique

**Dans un dossier spécifique** si le contrôleur :
- ✅ Est lié à une page/fonctionnalité précise
- ✅ Contient de la logique métier
- ✅ Gère des données spécifiques

### 2. Imports relatifs

Quand vous déplacez un contrôleur, ajustez les imports :
```javascript
// Depuis la racine controllers/
import { DragSelection } from '../js/utils/drag-selection.js';

// Depuis un sous-dossier controllers/missions/
import { DragSelection } from '../../js/utils/drag-selection.js';
```

### 3. Enregistrement dans bootstrap.js

Organisez les imports par catégorie :
```javascript
// Contrôleurs partagés
import ModalController from './controllers/shared/modal_controller.js';

// Contrôleurs spécifiques
import MissionController from './controllers/missions/mission_controller.js';
```

## 📝 Checklist pour un nouveau contrôleur

- [ ] **Analyser** : Le contrôleur est-il réutilisable ?
- [ ] **Placer** : Dans `/shared/` ou dossier spécifique ?
- [ ] **Créer** : Le fichier avec la bonne convention de nommage
- [ ] **Importer** : Dans `bootstrap.js` avec le bon chemin
- [ ] **Enregistrer** : Avec `app.register()`
- [ ] **Tester** : Compilation et fonctionnement
- [ ] **Documenter** : Mettre à jour les README

## 🚀 Avantages obtenus

### Avant (structure plate)
```
controllers/
├── mission_management_controller.js
├── mission_modal_controller.js
├── user_management_controller.js
├── heures_management_controller.js
├── calendar_controller.js
└── ... (12+ fichiers mélangés)
```

### Après (structure organisée)
```
controllers/
├── shared/
│   └── modal_selection_controller.js
├── missions/
│   ├── mission_management_controller.js
│   └── mission_modal_controller.js
├── users/
│   └── user_management_controller.js
└── heures/
    └── heures_management_controller.js
```

## 🔄 Migration d'un contrôleur existant

1. **Identifier** le dossier cible
2. **Déplacer** le fichier
3. **Corriger** les imports relatifs
4. **Mettre à jour** `bootstrap.js`
5. **Compiler** et tester
6. **Documenter** le changement
