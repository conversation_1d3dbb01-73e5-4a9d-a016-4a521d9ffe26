<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Ajout du système de rôles métiers et de validation des segments et primes
 */
final class Version20250724000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute le champ role_metier à User, les champs de validation à Segment, et crée la table Prime';
    }

    public function up(Schema $schema): void
    {
        // Ajouter le champ role_metier à la table user
        $this->addSql('ALTER TABLE `user` ADD role_metier VARCHAR(50) DEFAULT \'user\'');

        // Ajouter les champs de validation à la table segment
        $this->addSql('ALTER TABLE segment ADD valide TINYINT(1) DEFAULT 0');
        $this->addSql('ALTER TABLE segment ADD date_validation DATETIME DEFAULT NULL');
        $this->addSql('ALTER TABLE segment ADD valide_par_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE segment ADD INDEX IDX_1881F565A76ED395 (valide_par_id)');
        $this->addSql('ALTER TABLE segment ADD CONSTRAINT FK_1881F565A76ED395 FOREIGN KEY (valide_par_id) REFERENCES `user` (id)');

        // Créer la table prime
        $this->addSql('CREATE TABLE prime (
            id INT AUTO_INCREMENT NOT NULL, 
            user_id INT NOT NULL, 
            mission_id INT NOT NULL, 
            montant NUMERIC(10, 2) NOT NULL, 
            periode_debut DATE NOT NULL, 
            periode_fin DATE NOT NULL, 
            validation_responsable_osi TINYINT(1) DEFAULT 0, 
            date_validation_responsable_osi DATETIME DEFAULT NULL, 
            valide_par_responsable_osi_id INT DEFAULT NULL, 
            validation_assistante_rh TINYINT(1) DEFAULT 0, 
            date_validation_assistante_rh DATETIME DEFAULT NULL, 
            valide_par_assistante_rh_id INT DEFAULT NULL, 
            date_creation DATETIME NOT NULL, 
            date_modification DATETIME DEFAULT NULL, 
            INDEX IDX_C0A8BFCDA76ED395 (user_id), 
            INDEX IDX_C0A8BFCDBE6CAE90 (mission_id), 
            INDEX IDX_C0A8BFCD8B8E8428 (valide_par_responsable_osi_id), 
            INDEX IDX_C0A8BFCD2C5FC7E0 (valide_par_assistante_rh_id), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_C0A8BFCDA76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)');
        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_C0A8BFCDBE6CAE90 FOREIGN KEY (mission_id) REFERENCES mission (id)');
        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_C0A8BFCD8B8E8428 FOREIGN KEY (valide_par_responsable_osi_id) REFERENCES `user` (id)');
        $this->addSql('ALTER TABLE prime ADD CONSTRAINT FK_C0A8BFCD2C5FC7E0 FOREIGN KEY (valide_par_assistante_rh_id) REFERENCES `user` (id)');
    }

    public function down(Schema $schema): void
    {
        // Supprimer la table prime
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_C0A8BFCDA76ED395');
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_C0A8BFCDBE6CAE90');
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_C0A8BFCD8B8E8428');
        $this->addSql('ALTER TABLE prime DROP FOREIGN KEY FK_C0A8BFCD2C5FC7E0');
        $this->addSql('DROP TABLE prime');

        // Supprimer les champs de validation de la table segment
        $this->addSql('ALTER TABLE segment DROP FOREIGN KEY FK_1881F565A76ED395');
        $this->addSql('DROP INDEX IDX_1881F565A76ED395 ON segment');
        $this->addSql('ALTER TABLE segment DROP valide, DROP date_validation, DROP valide_par_id');

        // Supprimer le champ role_metier de la table user
        $this->addSql('ALTER TABLE `user` DROP role_metier');
    }
}
