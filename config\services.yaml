# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    images_directory: '%kernel.project_dir%/public/assets/uploads/images'
    upload_directory: '%kernel.project_dir%/public/assets/uploads/images'
    upload_file: '%kernel.project_dir%/public/assets/fichiers'
    upload_facture: '%kernel.project_dir%/public/assets/uploads/facture'
    forfait_pdf_directory: '%kernel.project_dir%/public/assets/uploads/forfait'


services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    App\Service\LdapService:
        arguments:
            $host: '%env(LDAP_HOST)%'
            $username: '%env(LDAP_USERNAME)%'
            $password: '%env(LDAP_PASSWORD)%'
