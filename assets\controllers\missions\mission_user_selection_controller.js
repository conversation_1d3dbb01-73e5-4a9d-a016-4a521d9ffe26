import { Controller } from '@hotwired/stimulus';
import { debounce } from '../../js/utils/throttle.js';
import { DragSelection } from '../../js/utils/drag-selection.js';

/**
 * Controller Stimulus pour la sélection d'utilisateurs dans les missions
 * Réutilise la logique de sélection mais adaptée aux missions
 */
export default class extends Controller {
    static targets = ['createContainer', 'editContainer', 'createSearch', 'editSearch', 'createSelected', 'editSelected'];
    static values = {
        usersUrl: String
    };

    connect() {
        this.allUsers = [];
        this.createSelectedUserIds = [];
        this.editSelectedUserIds = [];
        this.setupSearchHandlers();
        this.setupDragSelection();
        // Ne pas charger automatiquement les utilisateurs au connect
        // Ils seront chargés quand les modales s'ouvrent
    }

    disconnect() {
        // Nettoyer les instances de drag selection
        if (this.createDragSelection) {
            this.createDragSelection.destroy();
        }
        if (this.editDragSelection) {
            this.editDragSelection.destroy();
        }
    }

    /**
     * Configure les gestionnaires de recherche avec debounce
     */
    setupSearchHandlers() {
        this.debouncedCreateSearch = debounce(() => {
            this.renderUsers('create');
        }, 300);

        this.debouncedEditSearch = debounce(() => {
            this.renderUsers('edit');
        }, 300);

        if (this.hasCreateSearchTarget) {
            this.createSearchTarget.addEventListener('input', this.debouncedCreateSearch);
        }

        if (this.hasEditSearchTarget) {
            this.editSearchTarget.addEventListener('input', this.debouncedEditSearch);
        }
    }

    /**
     * Configure le drag-to-select pour les conteneurs
     */
    setupDragSelection() {
        // Les instances seront créées quand les conteneurs seront disponibles
        this.createDragSelection = null;
        this.editDragSelection = null;
    }

    /**
     * Configure les listeners pour les changements de checkbox pour un contexte donné
     */
    setupCheckboxListeners(context) {
        const isEdit = context === 'edit';
        const container = isEdit ?
            (this.hasEditContainerTarget ? this.editContainerTarget : null) :
            (this.hasCreateContainerTarget ? this.createContainerTarget : null);

        if (!container) return;

        // Supprimer les anciens listeners s'ils existent
        if (container._checkboxListener) {
            container.removeEventListener('change', container._checkboxListener);
        }

        // Ajouter le nouveau listener
        container._checkboxListener = (e) => {
            if (e.target.classList.contains('user-checkbox')) {
                this.handleCheckboxChange(e.target, context);
            }
        };

        container.addEventListener('change', container._checkboxListener);
    }

    /**
     * Initialise le drag-to-select pour un contexte donné
     */
    initializeDragSelection(context) {
        const isEdit = context === 'edit';
        const container = isEdit ?
            (this.hasEditContainerTarget ? this.editContainerTarget : null) :
            (this.hasCreateContainerTarget ? this.createContainerTarget : null);

        if (!container) return;

        // Détruire l'instance existante si elle existe
        const existingInstance = isEdit ? this.editDragSelection : this.createDragSelection;
        if (existingInstance) {
            existingInstance.destroy();
        }

        // Créer une nouvelle instance
        const dragSelection = new DragSelection(container, {
            itemSelector: '.user-item',
            selectedClass: 'selected',
            selectingClass: 'selecting',
            useContainer: true,
            onSelectionChange: (selectedIds) => {
                // Pour l'édition, on doit préserver les utilisateurs déjà sélectionnés
                // et ajouter/retirer seulement ceux modifiés par DragSelection
                if (isEdit) {
                    // Synchroniser notre tableau avec DragSelection sans perdre les pré-sélectionnés
                    this.syncEditSelectionWithDrag(selectedIds);
                } else {
                    this.createSelectedUserIds = Array.from(selectedIds);
                    this.updateSelectedUsers('create');
                }
                // Synchroniser les checkboxes après changement de sélection
                this.syncCheckboxes(context);
            },
            onItemClick: (item, itemId) => {
                // DragSelection a déjà modifié sa sélection, on synchronise juste la checkbox
                const checkbox = item.querySelector('.user-checkbox');
                if (checkbox) {
                    const dragSelection = isEdit ? this.editDragSelection : this.createDragSelection;
                    checkbox.checked = dragSelection.selectedItems.has(itemId);

                    // Synchroniser nos tableaux avec DragSelection
                    const selectedIds = isEdit ? this.editSelectedUserIds : this.createSelectedUserIds;
                    const index = selectedIds.indexOf(itemId);

                    if (checkbox.checked && index === -1) {
                        selectedIds.push(itemId);
                    } else if (!checkbox.checked && index > -1) {
                        selectedIds.splice(index, 1);
                    }

                    this.updateSelectedUsers(isEdit ? 'edit' : 'create');
                }
            }
        });

        // Stocker l'instance
        if (isEdit) {
            this.editDragSelection = dragSelection;
            // Pour l'édition, synchroniser DragSelection avec les utilisateurs déjà sélectionnés
            this.syncDragWithEditSelection();
        } else {
            this.createDragSelection = dragSelection;
        }
    }

    /**
     * Charge les utilisateurs depuis l'API
     */
    async loadUsers() {
        // Vérifier que l'URL est disponible
        if (!this.usersUrlValue) {
            console.warn('URL des utilisateurs non définie');
            return;
        }

        try {
            const response = await window.ajax.get(this.usersUrlValue);

            // Gérer les différents formats de réponse
            const payload = response.data ?? response;
            let users;

            if (Array.isArray(payload)) {
                users = payload;
            } else if (Array.isArray(payload['hydra:member'])) {
                users = payload['hydra:member'];
            } else if (Array.isArray(payload.data)) {
                users = payload.data;
            } else {
                console.error('Réponse utilisateurs invalide:', response);
                this.allUsers = [];
                return;
            }

            this.allUsers = users;
            // Ne pas rendre automatiquement ici, laisser les méthodes appelantes le faire
        } catch (error) {
            console.error('Erreur lors du chargement des utilisateurs:', error);
            window.showToast?.error('Erreur lors du chargement des utilisateurs');
            this.allUsers = [];
        }
    }

    /**
     * Rend la liste des utilisateurs pour un contexte donné
     */
    renderUsers(context) {
        const isEdit = context === 'edit';

        // Vérifier l'existence des targets avant de les utiliser
        const container = isEdit ?
            (this.hasEditContainerTarget ? this.editContainerTarget : null) :
            (this.hasCreateContainerTarget ? this.createContainerTarget : null);

        const searchTarget = isEdit ?
            (this.hasEditSearchTarget ? this.editSearchTarget : null) :
            (this.hasCreateSearchTarget ? this.createSearchTarget : null);

        const selectedIds = isEdit ? this.editSelectedUserIds : this.createSelectedUserIds;

        if (!container || !searchTarget) {
            console.warn(`Targets manquants pour le contexte ${context}:`, {
                container: !!container,
                searchTarget: !!searchTarget
            });
            return;
        }

        const searchTerm = searchTarget.value.toLowerCase();
        container.innerHTML = '';

        const filteredUsers = this.allUsers.filter(user =>
            user.nom.toLowerCase().includes(searchTerm) ||
            user.prenom.toLowerCase().includes(searchTerm) ||
            user.email.toLowerCase().includes(searchTerm) ||
            (user.roleDisplay && user.roleDisplay.toLowerCase().includes(searchTerm))
        );

        filteredUsers.forEach(user => {
            const userDiv = document.createElement('div');
            const isSelected = selectedIds.includes(user.id);

            // Ajouter les classes nécessaires pour le drag-to-select
            userDiv.className = `user-item flex items-center p-2 cursor-pointer transition-colors ${
                isSelected ? 'selected bg-blue-50 hover:bg-blue-100 border-l-4 border-blue-500' : 'hover:bg-gray-100'
            }`;

            // Ajouter l'attribut data-user-id pour le drag-to-select
            userDiv.dataset.userId = user.id;

            userDiv.innerHTML = `
                <input type="checkbox" ${isSelected ? 'checked' : ''} class="user-checkbox mr-2 text-blue-600" id="user-${user.id}-${context}" data-user-id="${user.id}">
                <div class="flex-1">
                    <span class="text-sm font-medium ${isSelected ? 'text-blue-900' : 'text-gray-900'}">${user.prenom} ${user.nom}</span>
                    <span class="text-xs text-gray-500 ml-2">(${user.roleDisplay || 'Utilisateur'})</span>
                </div>
            `;

            container.appendChild(userDiv);
        });

        // Initialiser le drag-to-select et les listeners de checkbox après le rendu
        this.initializeDragSelection(context);
        this.setupCheckboxListeners(context);
    }

    /**
     * Bascule la sélection d'un utilisateur
     */
    toggleUser(userId, context) {
        const isEdit = context === 'edit';
        const selectedIds = isEdit ? this.editSelectedUserIds : this.createSelectedUserIds;
        const index = selectedIds.indexOf(userId);

        if (index > -1) {
            selectedIds.splice(index, 1);
        } else {
            selectedIds.push(userId);
        }

        // Synchroniser avec l'instance de drag-selection
        const dragSelection = isEdit ? this.editDragSelection : this.createDragSelection;
        if (dragSelection) {
            if (selectedIds.includes(userId)) {
                dragSelection.selectedItems.add(userId);
            } else {
                dragSelection.selectedItems.delete(userId);
            }
        }

        // Mettre à jour la checkbox
        const checkbox = document.getElementById(`user-${userId}-${context}`);
        if (checkbox) {
            checkbox.checked = selectedIds.includes(userId);
        }

        this.updateSelectedUsers(context);
        this.renderUsers(context);
    }

    /**
     * Gère les changements d'état des checkboxes
     */
    handleCheckboxChange(checkbox, context = 'create') {
        // Empêcher les événements en boucle
        if (checkbox._updating) return;

        const userId = parseInt(checkbox.dataset.userId);
        const userItem = checkbox.closest('.user-item');
        const sel = context === 'edit'
            ? this.editSelectedUserIds
            : this.createSelectedUserIds;

        if (checkbox.checked) {
            if (!sel.includes(userId)) {
                sel.push(userId);
            }
            userItem.classList.add('selected');
            // Synchroniser avec DragSelection
            (context === 'edit' ? this.editDragSelection : this.createDragSelection)
                ?.selectedItems.add(userId);
        } else {
            const idx = sel.indexOf(userId);
            if (idx > -1) sel.splice(idx, 1);
            userItem.classList.remove('selected');
            // Synchroniser avec DragSelection
            (context === 'edit' ? this.editDragSelection : this.createDragSelection)
                ?.selectedItems.delete(userId);
        }

        this.updateSelectedUsers(context);
    }

    /**
     * Synchronise la sélection d'édition avec DragSelection en préservant les pré-sélectionnés
     */
    syncEditSelectionWithDrag(dragSelectedIds) {
        // Obtenir tous les utilisateurs visibles dans le conteneur
        const container = this.hasEditContainerTarget ? this.editContainerTarget : null;
        if (!container) return;

        const visibleUserIds = Array.from(container.querySelectorAll('.user-item'))
            .map(item => parseInt(item.dataset.userId))
            .filter(id => !isNaN(id));

        // Séparer les utilisateurs pré-sélectionnés (non visibles) des visibles
        const preSelectedIds = this.editSelectedUserIds.filter(id => !visibleUserIds.includes(id));
        const visibleSelectedIds = Array.from(dragSelectedIds);

        // Combiner les pré-sélectionnés avec les sélectionnés visibles
        this.editSelectedUserIds = [...preSelectedIds, ...visibleSelectedIds];
        this.updateSelectedUsers('edit');
    }

    /**
     * Synchronise DragSelection avec les utilisateurs déjà sélectionnés en édition
     */
    syncDragWithEditSelection() {
        if (!this.editDragSelection) return;

        const container = this.hasEditContainerTarget ? this.editContainerTarget : null;
        if (!container) return;

        // Ajouter à DragSelection tous les utilisateurs visibles qui sont déjà sélectionnés
        const visibleItems = container.querySelectorAll('.user-item');
        visibleItems.forEach(item => {
            const userId = parseInt(item.dataset.userId);
            if (!isNaN(userId) && this.editSelectedUserIds.includes(userId)) {
                this.editDragSelection.selectedItems.add(userId);
                item.classList.add('selected');
            }
        });
    }

    /**
     * Synchronise les checkboxes avec la sélection actuelle
     */
    syncCheckboxes(context) {
        const isEdit = context === 'edit';
        const container = isEdit ?
            (this.hasEditContainerTarget ? this.editContainerTarget : null) :
            (this.hasCreateContainerTarget ? this.createContainerTarget : null);
        const selectedIds = isEdit ? this.editSelectedUserIds : this.createSelectedUserIds;

        if (!container) return;

        const checkboxes = container.querySelectorAll('.user-checkbox');
        checkboxes.forEach(checkbox => {
            const userId = parseInt(checkbox.dataset.userId);
            const userItem = checkbox.closest('.user-item');
            const isSelected = selectedIds.includes(userId);

            // Empêcher les événements en boucle pendant la synchronisation
            checkbox._updating = true;
            checkbox.checked = isSelected;
            checkbox._updating = false;

            if (isSelected) {
                userItem.classList.add('selected');
            } else {
                userItem.classList.remove('selected');
            }
        });
    }

    /**
     * Met à jour l'affichage des utilisateurs sélectionnés
     */
    updateSelectedUsers(context) {
        const isEdit = context === 'edit';
        const container = isEdit ? (this.hasEditSelectedTarget ? this.editSelectedTarget : null) : (this.hasCreateSelectedTarget ? this.createSelectedTarget : null);
        const hiddenInput = document.getElementById(isEdit ? 'editSelectedUserIds' : 'selectedUserIds');
        const selectedIds = isEdit ? this.editSelectedUserIds : this.createSelectedUserIds;

        if (!container || !hiddenInput) return;

        container.innerHTML = '';
        hiddenInput.value = selectedIds.join(',');

        selectedIds.forEach(userId => {
            const user = this.allUsers.find(u => u.id === userId);
            if (user) {
                const tag = document.createElement('span');
                tag.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800';
                tag.innerHTML = `
                    ${user.prenom} ${user.nom}
                    <button type="button" class="ml-1 text-blue-600 hover:text-blue-800">×</button>
                `;

                // Ajouter l'événement de suppression
                tag.querySelector('button').addEventListener('click', () => {
                    this.toggleUser(userId, context);
                });

                container.appendChild(tag);
            }
        });
    }

    /**
     * Sélectionne des utilisateurs depuis l'extérieur (pour l'édition)
     */
    selectUsers(event) {
        const { userIds } = event.detail;
        // Ajouter les nouveaux utilisateurs à la sélection existante au lieu de remplacer
        userIds.forEach(userId => {
            if (!this.editSelectedUserIds.includes(userId)) {
                this.editSelectedUserIds.push(userId);
                // Synchroniser avec DragSelection
                if (this.editDragSelection) {
                    this.editDragSelection.selectedItems.add(userId);
                }
            }
        });
        this.updateSelectedUsers('edit');
        this.renderUsers('edit');
    }

    /**
     * Vide la sélection
     */
    clearSelection() {
        this.createSelectedUserIds = [];
        this.editSelectedUserIds = [];
        this.updateSelectedUsers('create');
        this.updateSelectedUsers('edit');
        this.renderUsers('create');
        this.renderUsers('edit');
    }

    /**
     * Charge les utilisateurs pour l'édition
     */
    loadUsersForEdit() {
        if (this.allUsers.length === 0) {
            this.loadUsers().then(() => {
                if (this.hasEditContainerTarget) {
                    this.renderUsers('edit');
                }
            });
        } else {
            if (this.hasEditContainerTarget) {
                this.renderUsers('edit');
            }
        }
    }

    /**
     * Gestionnaire d'événement pour charger les utilisateurs
     */
    loadUsersEvent() {
        // Vérifier qu'au moins un conteneur est disponible avant de charger
        if (!this.hasCreateContainerTarget && !this.hasEditContainerTarget) {
            console.warn('Aucun conteneur d\'utilisateurs disponible pour le chargement');
            return;
        }

        this.loadUsers().then(() => {
            if (this.hasEditContainerTarget) {
                this.renderUsers('edit');
            }
            if (this.hasCreateContainerTarget) {
                this.renderUsers('create');
            }
        }).catch(error => {
            console.error('Erreur lors du chargement des utilisateurs:', error);
        });
    }

    /**
     * Sélectionne tous les utilisateurs pour la création
     */
    selectAllCreate() {
        if (this.createDragSelection) {
            this.createDragSelection.selectAll();
        }
        // Synchroniser les checkboxes
        this.syncCheckboxes('create');
    }

    /**
     * Désélectionne tous les utilisateurs pour la création
     */
    clearSelectionCreate() {
        this.createSelectedUserIds = [];
        if (this.createDragSelection) {
            this.createDragSelection.clearSelection();
        }
        this.updateSelectedUsers('create');
        // Synchroniser les checkboxes
        this.syncCheckboxes('create');
    }

    /**
     * Sélectionne tous les utilisateurs pour l'édition
     */
    selectAllEdit() {
        if (this.editDragSelection) {
            this.editDragSelection.selectAll();
        }
        // Synchroniser les checkboxes
        this.syncCheckboxes('edit');
    }

    /**
     * Désélectionne tous les utilisateurs pour l'édition
     */
    clearSelectionEdit() {
        this.editSelectedUserIds = [];
        if (this.editDragSelection) {
            this.editDragSelection.clearSelection();
        }
        this.updateSelectedUsers('edit');
        // Synchroniser les checkboxes
        this.syncCheckboxes('edit');
    }
}
