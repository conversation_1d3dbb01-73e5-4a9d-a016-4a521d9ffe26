<?php

namespace App\Service;

use App\Entity\Collaborateur;
use App\Entity\Segment;

class RecuperationCalculatorService
{
    private HeureCalculatorService $heureCalculator;

    public function __construct(HeureCalculatorService $heureCalculator)
    {
        $this->heureCalculator = $heureCalculator;
    }

    /**
     * Calcule la récupération en mode horaire
     */
    public function calculRecuperationHoraire(array $segments): float
    {
        $totalRecuperation = 0;

        foreach ($segments as $segment) {
            if ($segment->isVoyage()) {
                $heuresHorsPlages = $this->calculHeuresVoyageHorsPlages($segment);
                $totalRecuperation += $heuresHorsPlages;
            }
        }

        return $totalRecuperation;
    }

    /**
     * Calcule la récupération en forfait jour
     */
    public function calculRecuperationForfaitJour(array $segments): float
    {
        $totalHeuresVoyageHorsPlages = 0;

        foreach ($segments as $segment) {
            if ($segment->isVoyage()) {
                $heuresHorsPlages = $this->calculHeuresVoyageHorsPlages($segment);
                $totalHeuresVoyageHorsPlages += $heuresHorsPlages;
            }
        }

        // Règle forfait jour : T ≤ 4h → 0.5 jour, sinon → 1 jour
        if ($totalHeuresVoyageHorsPlages <= 4) {
            return 0.5;
        } else {
            return 1.0;
        }
    }

    /**
     * Calcule la récupération selon le mode du collaborateur
     */
    public function calculRecuperation(Collaborateur $collaborateur, array $segments): array
    {
        if ($collaborateur->isForfaitJour()) {
            $recuperation = $this->calculRecuperationForfaitJour($segments);
            $unite = 'jours';
        } else {
            $recuperation = $this->calculRecuperationHoraire($segments);
            $unite = 'heures';
        }

        return [
            'recuperation' => $recuperation,
            'unite' => $unite,
            'mode' => $collaborateur->isForfaitJour() ? 'forfait_jour' : 'horaire',
            'detail' => $this->getDetailRecuperation($segments, $collaborateur->isForfaitJour())
        ];
    }

    /**
     * Calcule les heures de voyage hors plages normales
     */
    private function calculHeuresVoyageHorsPlages(Segment $segment): float
    {
        if (!$segment->isVoyage()) {
            return 0;
        }

        $debut = $segment->getDateHeureDebut();
        $fin = $segment->getDateHeureFin();

        // Si c'est un week-end ou jour férié, tout est hors plages
        if ($this->isWeekendOrHoliday($debut)) {
            return $this->heureCalculator->getDureeHeures($segment);
        }

        return $this->calculHeuresHorsPlagesNormales($debut, $fin);
    }

    /**
     * Calcule les heures hors plages normales (09:00-12:00, 14:00-18:00)
     */
    private function calculHeuresHorsPlagesNormales(\DateTimeInterface $debut, \DateTimeInterface $fin): float
    {
        $heuresHorsPlages = 0;
        $current = clone $debut;

        while ($current < $fin) {
            $nextHour = clone $current;
            $nextHour->modify('+1 hour');
            
            if ($nextHour > $fin) {
                $nextHour = $fin;
            }

            $heure = (int) $current->format('H');
            
            // Vérifier si l'heure est hors plages normales
            if ($this->isHeureHorsPlages($heure)) {
                $dureeSegment = ($nextHour->getTimestamp() - $current->getTimestamp()) / 3600;
                $heuresHorsPlages += $dureeSegment;
            }

            $current = $nextHour;
        }

        return $heuresHorsPlages;
    }

    /**
     * Vérifie si une heure est hors plages normales
     */
    private function isHeureHorsPlages(int $heure): bool
    {
        // Plages normales : 09:00-12:00 et 14:00-18:00
        return !($heure >= 9 && $heure < 12) && !($heure >= 14 && $heure < 18);
    }

    /**
     * Vérifie si c'est un week-end ou jour férié
     */
    private function isWeekendOrHoliday(\DateTimeInterface $date): bool
    {
        $dayOfWeek = (int) $date->format('N');
        
        // Week-end
        if ($dayOfWeek >= 6) {
            return true;
        }

        // Jours fériés français (liste simplifiée)
        return $this->isFrenchHoliday($date);
    }

    /**
     * Vérifie si c'est un jour férié français
     */
    private function isFrenchHoliday(\DateTimeInterface $date): bool
    {
        $year = (int) $date->format('Y');
        $month = (int) $date->format('m');
        $day = (int) $date->format('d');

        // Jours fériés fixes
        $fixedHolidays = [
            '01-01', // Nouvel An
            '05-01', // Fête du Travail
            '05-08', // Victoire 1945
            '07-14', // Fête Nationale
            '08-15', // Assomption
            '11-01', // Toussaint
            '11-11', // Armistice
            '12-25', // Noël
        ];

        $dateStr = sprintf('%02d-%02d', $month, $day);
        if (in_array($dateStr, $fixedHolidays)) {
            return true;
        }

        // Jours fériés variables (Pâques, etc.)
        $easter = easter_date($year);
        $easterMonth = (int) date('m', $easter);
        $easterDay = (int) date('d', $easter);

        // Lundi de Pâques
        $easterMonday = mktime(0, 0, 0, $easterMonth, $easterDay + 1, $year);
        if ($month === (int) date('m', $easterMonday) && $day === (int) date('d', $easterMonday)) {
            return true;
        }

        // Ascension (39 jours après Pâques)
        $ascension = mktime(0, 0, 0, $easterMonth, $easterDay + 39, $year);
        if ($month === (int) date('m', $ascension) && $day === (int) date('d', $ascension)) {
            return true;
        }

        // Lundi de Pentecôte (50 jours après Pâques)
        $pentecost = mktime(0, 0, 0, $easterMonth, $easterDay + 50, $year);
        if ($month === (int) date('m', $pentecost) && $day === (int) date('d', $pentecost)) {
            return true;
        }

        return false;
    }

    /**
     * Obtient le détail de la récupération
     */
    private function getDetailRecuperation(array $segments, bool $forfaitJour): array
    {
        $detail = [];
        $totalHeuresHorsPlages = 0;

        foreach ($segments as $segment) {
            if ($segment->isVoyage()) {
                $heuresHorsPlages = $this->calculHeuresVoyageHorsPlages($segment);
                $totalHeuresHorsPlages += $heuresHorsPlages;
                
                $detail[] = [
                    'segment' => $segment,
                    'heuresHorsPlages' => $heuresHorsPlages,
                    'isWeekend' => $this->isWeekendOrHoliday($segment->getDateHeureDebut())
                ];
            }
        }

        return [
            'segments' => $detail,
            'totalHeuresHorsPlages' => $totalHeuresHorsPlages,
            'calculForfaitJour' => $forfaitJour ? [
                'seuil4h' => 4,
                'recuperation' => $totalHeuresHorsPlages <= 4 ? 0.5 : 1.0
            ] : null
        ];
    }

    /**
     * Calcule la récupération pour une période
     */
    public function calculRecuperationPeriode(Collaborateur $collaborateur, array $segments, \DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        $segmentsPeriode = array_filter($segments, function($segment) use ($debut, $fin) {
            return $segment->getDateHeureDebut() >= $debut && $segment->getDateHeureFin() <= $fin;
        });

        return $this->calculRecuperation($collaborateur, $segmentsPeriode);
    }
}
