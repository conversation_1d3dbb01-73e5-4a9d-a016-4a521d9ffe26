{% extends 'base.html.twig' %}

{% block title %}Tableau de bord - Validation{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Tableau de bord - Validation</h1>
                <div class="btn-group">
                    <a href="{{ path('app_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Retour au dashboard principal
                    </a>
                </div>
            </div>

            <!-- Informations utilisateur -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-user"></i> {{ app.user.nomComplet }}
                            </h5>
                            <p class="card-text">
                                <strong>Rôle :</strong> 
                                {% if user_role == 'responsable_mission' %}
                                    <span class="badge badge-primary">Responsable Mission</span>
                                {% elseif user_role == 'assistante_rh' %}
                                    <span class="badge badge-info">Assistante RH</span>
                                {% elseif user_role == 'responsable_paie' %}
                                    <span class="badge badge-success">Responsable Paie</span>
                                {% elseif user_role == 'super_admin' %}
                                    <span class="badge badge-danger">Super Admin</span>
                                {% else %}
                                    <span class="badge badge-secondary">Utilisateur</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions disponibles -->
            <div class="row mb-4">
                {% if app.user.canValidateSegments %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">Validation des Segments</h5>
                                <p class="card-text">Valider les heures saisies par les utilisateurs</p>
                                {% if data.segments is defined %}
                                    <div class="mb-3">
                                        <span class="badge badge-warning">{{ data.segments.en_attente }} en attente</span>
                                    </div>
                                {% endif %}
                                <a href="{{ path('app_validation_segments') }}" class="btn btn-primary">
                                    <i class="fas fa-check"></i> Accéder
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}

                {% if app.user.canValidatePrimesResponsable or app.user.canValidatePrimesRh %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-euro-sign fa-3x text-success mb-3"></i>
                                <h5 class="card-title">Validation des Primes</h5>
                                <p class="card-text">
                                    {% if app.user.canValidatePrimesResponsable %}
                                        Valider les primes calculées
                                    {% else %}
                                        Validation finale RH des primes
                                    {% endif %}
                                </p>
                                {% if data.primes is defined %}
                                    <div class="mb-3">
                                        {% if app.user.canValidatePrimesResponsable %}
                                            <span class="badge badge-warning">{{ data.primes.en_attente_responsable }} en attente</span>
                                        {% else %}
                                            <span class="badge badge-primary">{{ data.primes.en_attente_rh }} en attente RH</span>
                                        {% endif %}
                                    </div>
                                {% endif %}
                                <a href="{{ path('app_validation_primes') }}" class="btn btn-success">
                                    <i class="fas fa-check"></i> Accéder
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}

                {% if app.user.canAccessValidatedPrimes %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-file-invoice-dollar fa-3x text-info mb-3"></i>
                                <h5 class="card-title">Primes Validées</h5>
                                <p class="card-text">Consulter les primes validées pour la paie</p>
                                {% if data.primes is defined %}
                                    <div class="mb-3">
                                        <span class="badge badge-success">{{ data.primes.validees }} validées</span>
                                    </div>
                                {% endif %}
                                <a href="{{ path('app_primes') }}" class="btn btn-info">
                                    <i class="fas fa-eye"></i> Consulter
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}

                {% if app.user.canCreateMissions %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-plus-circle fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">Gestion des Missions</h5>
                                <p class="card-text">Créer et gérer les missions</p>
                                <a href="{{ path('app_missions') }}" class="btn btn-warning">
                                    <i class="fas fa-cogs"></i> Accéder
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}

                {% if app.user.canAssignUsersToMissions %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-3x text-secondary mb-3"></i>
                                <h5 class="card-title">Gestion des Utilisateurs</h5>
                                <p class="card-text">Assigner des utilisateurs aux missions</p>
                                <a href="{{ path('app_users') }}" class="btn btn-secondary">
                                    <i class="fas fa-user-cog"></i> Accéder
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- Statistiques globales -->
            {% if data.segments is defined or data.primes is defined %}
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-bar"></i> Statistiques
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% if data.segments is defined %}
                                        <div class="col-md-6">
                                            <h6>Segments</h6>
                                            <div class="progress mb-2">
                                                <div class="progress-bar bg-success" role="progressbar" 
                                                     style="width: {{ data.segments.pourcentage_valides }}%">
                                                    {{ data.segments.pourcentage_valides }}%
                                                </div>
                                            </div>
                                            <small class="text-muted">
                                                {{ data.segments.valides }} validés sur {{ data.segments.total }} total
                                            </small>
                                        </div>
                                    {% endif %}

                                    {% if data.primes is defined %}
                                        <div class="col-md-6">
                                            <h6>Primes</h6>
                                            <div class="row">
                                                <div class="col-4 text-center">
                                                    <div class="text-warning">
                                                        <strong>{{ data.primes.en_attente_responsable }}</strong>
                                                    </div>
                                                    <small>En attente responsable</small>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="text-primary">
                                                        <strong>{{ data.primes.en_attente_rh }}</strong>
                                                    </div>
                                                    <small>En attente RH</small>
                                                </div>
                                                <div class="col-4 text-center">
                                                    <div class="text-success">
                                                        <strong>{{ data.primes.validees }}</strong>
                                                    </div>
                                                    <small>Validées</small>
                                                </div>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Aide et documentation -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle"></i> Aide
                            </h5>
                        </div>
                        <div class="card-body">
                            <h6>Processus de validation :</h6>
                            <ol>
                                <li><strong>Saisie des heures</strong> : Les utilisateurs saisissent leurs segments de travail</li>
                                <li><strong>Validation des segments</strong> : Le responsable mission valide les heures saisies</li>
                                <li><strong>Génération des primes</strong> : Les primes sont calculées automatiquement</li>
                                <li><strong>Validation responsable</strong> : Le responsable mission valide les montants des primes</li>
                                <li><strong>Validation RH</strong> : L'assistante RH effectue la validation finale</li>
                                <li><strong>Traitement paie</strong> : Le responsable paie accède aux primes validées</li>
                            </ol>
                            
                            {% if app.user.isSuperAdmin %}
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-crown"></i> 
                                    <strong>Super Admin :</strong> Vous avez accès à toutes les fonctionnalités et pouvez intervenir à toutes les étapes du processus.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
