<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250718062406 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // Cette migration est obsolète car la colonne collaborateur_id a déjà été supprimée
        // Vérifier si la colonne existe avant de la supprimer
        if ($schema->hasTable('mission') && $schema->getTable('mission')->hasColumn('collaborateur_id')) {
            $this->addSql('ALTER TABLE mission DROP FOREIGN KEY FK_9067F23CA848E3B1');
            $this->addSql('DROP INDEX IDX_9067F23CA848E3B1 ON mission');
            $this->addSql('ALTER TABLE mission DROP collaborateur_id');
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE mission ADD collaborateur_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE mission ADD CONSTRAINT FK_9067F23CA848E3B1 FOREIGN KEY (collaborateur_id) REFERENCES collaborateur (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE INDEX IDX_9067F23CA848E3B1 ON mission (collaborateur_id)');
    }
}
