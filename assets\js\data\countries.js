/**
 * Liste des pays avec leur zone correspondante
 * EURO = Zone Euro, HORS_EURO = Hors Zone Euro
 */
export const COUNTRIES_DATA = {
    // Zone Euro
    'Allemagne': 'EURO',
    'Autriche': 'EURO',
    'Belgique': 'EURO',
    'Chypre': 'EURO',
    'Croatie': 'EURO',
    'Espagne': 'EURO',
    'Estonie': 'EURO',
    'Finlande': 'EURO',
    'France': 'EURO',
    'Grèce': 'EURO',
    'Irlande': 'EURO',
    'Italie': 'EURO',
    'Lettonie': 'EURO',
    'Lituanie': 'EURO',
    'Luxembourg': 'EURO',
    'Malte': 'EURO',
    'Pays-Bas': 'EURO',
    'Portugal': 'EURO',
    'Slovaquie': 'EURO',
    'Slovénie': 'EURO',
    
    // Hors Zone Euro - Europe
    'Albanie': 'HORS_EURO',
    'Andorre': 'HORS_EURO',
    'Biélorussie': 'HORS_EURO',
    'Bosnie-Herzégovine': 'HORS_EURO',
    'Bulgarie': 'HORS_EURO',
    'Danemark': 'HORS_EURO',
    'Hongrie': 'HORS_EURO',
    'Islande': 'HORS_EURO',
    'Kosovo': 'HORS_EURO',
    'Liechtenstein': 'HORS_EURO',
    'Macédoine du Nord': 'HORS_EURO',
    'Moldavie': 'HORS_EURO',
    'Monaco': 'HORS_EURO',
    'Monténégro': 'HORS_EURO',
    'Norvège': 'HORS_EURO',
    'Pologne': 'HORS_EURO',
    'République tchèque': 'HORS_EURO',
    'Roumanie': 'HORS_EURO',
    'Royaume-Uni': 'HORS_EURO',
    'Russie': 'HORS_EURO',
    'Saint-Marin': 'HORS_EURO',
    'Serbie': 'HORS_EURO',
    'Suède': 'HORS_EURO',
    'Suisse': 'HORS_EURO',
    'Ukraine': 'HORS_EURO',
    'Vatican': 'HORS_EURO',
    
    // Afrique
    'Afrique du Sud': 'HORS_EURO',
    'Algérie': 'HORS_EURO',
    'Angola': 'HORS_EURO',
    'Bénin': 'HORS_EURO',
    'Botswana': 'HORS_EURO',
    'Burkina Faso': 'HORS_EURO',
    'Burundi': 'HORS_EURO',
    'Cameroun': 'HORS_EURO',
    'Cap-Vert': 'HORS_EURO',
    'République centrafricaine': 'HORS_EURO',
    'Tchad': 'HORS_EURO',
    'Comores': 'HORS_EURO',
    'République démocratique du Congo': 'HORS_EURO',
    'République du Congo': 'HORS_EURO',
    'Côte d\'Ivoire': 'HORS_EURO',
    'Djibouti': 'HORS_EURO',
    'Égypte': 'HORS_EURO',
    'Guinée équatoriale': 'HORS_EURO',
    'Érythrée': 'HORS_EURO',
    'Éthiopie': 'HORS_EURO',
    'Gabon': 'HORS_EURO',
    'Gambie': 'HORS_EURO',
    'Ghana': 'HORS_EURO',
    'Guinée': 'HORS_EURO',
    'Guinée-Bissau': 'HORS_EURO',
    'Kenya': 'HORS_EURO',
    'Lesotho': 'HORS_EURO',
    'Libéria': 'HORS_EURO',
    'Libye': 'HORS_EURO',
    'Madagascar': 'HORS_EURO',
    'Malawi': 'HORS_EURO',
    'Mali': 'HORS_EURO',
    'Mauritanie': 'HORS_EURO',
    'Maurice': 'HORS_EURO',
    'Maroc': 'HORS_EURO',
    'Mozambique': 'HORS_EURO',
    'Namibie': 'HORS_EURO',
    'Niger': 'HORS_EURO',
    'Nigeria': 'HORS_EURO',
    'Rwanda': 'HORS_EURO',
    'Sao Tomé-et-Principe': 'HORS_EURO',
    'Sénégal': 'HORS_EURO',
    'Seychelles': 'HORS_EURO',
    'Sierra Leone': 'HORS_EURO',
    'Somalie': 'HORS_EURO',
    'Soudan': 'HORS_EURO',
    'Soudan du Sud': 'HORS_EURO',
    'Eswatini': 'HORS_EURO',
    'Tanzanie': 'HORS_EURO',
    'Togo': 'HORS_EURO',
    'Tunisie': 'HORS_EURO',
    'Ouganda': 'HORS_EURO',
    'Zambie': 'HORS_EURO',
    'Zimbabwe': 'HORS_EURO',
    
    // Asie
    'Afghanistan': 'HORS_EURO',
    'Arabie saoudite': 'HORS_EURO',
    'Arménie': 'HORS_EURO',
    'Azerbaïdjan': 'HORS_EURO',
    'Bahreïn': 'HORS_EURO',
    'Bangladesh': 'HORS_EURO',
    'Bhoutan': 'HORS_EURO',
    'Birmanie': 'HORS_EURO',
    'Brunei': 'HORS_EURO',
    'Cambodge': 'HORS_EURO',
    'Chine': 'HORS_EURO',
    'Corée du Nord': 'HORS_EURO',
    'Corée du Sud': 'HORS_EURO',
    'Émirats arabes unis': 'HORS_EURO',
    'Géorgie': 'HORS_EURO',
    'Inde': 'HORS_EURO',
    'Indonésie': 'HORS_EURO',
    'Irak': 'HORS_EURO',
    'Iran': 'HORS_EURO',
    'Israël': 'HORS_EURO',
    'Japon': 'HORS_EURO',
    'Jordanie': 'HORS_EURO',
    'Kazakhstan': 'HORS_EURO',
    'Kirghizistan': 'HORS_EURO',
    'Koweït': 'HORS_EURO',
    'Laos': 'HORS_EURO',
    'Liban': 'HORS_EURO',
    'Malaisie': 'HORS_EURO',
    'Maldives': 'HORS_EURO',
    'Mongolie': 'HORS_EURO',
    'Népal': 'HORS_EURO',
    'Oman': 'HORS_EURO',
    'Ouzbékistan': 'HORS_EURO',
    'Pakistan': 'HORS_EURO',
    'Palestine': 'HORS_EURO',
    'Philippines': 'HORS_EURO',
    'Qatar': 'HORS_EURO',
    'Singapour': 'HORS_EURO',
    'Sri Lanka': 'HORS_EURO',
    'Syrie': 'HORS_EURO',
    'Tadjikistan': 'HORS_EURO',
    'Taïwan': 'HORS_EURO',
    'Thaïlande': 'HORS_EURO',
    'Timor oriental': 'HORS_EURO',
    'Turkménistan': 'HORS_EURO',
    'Turquie': 'HORS_EURO',
    'Viêt Nam': 'HORS_EURO',
    'Yémen': 'HORS_EURO',
    
    // Amérique du Nord
    'Canada': 'HORS_EURO',
    'États-Unis': 'HORS_EURO',
    'Mexique': 'HORS_EURO',
    
    // Amérique centrale et Caraïbes
    'Antigua-et-Barbuda': 'HORS_EURO',
    'Bahamas': 'HORS_EURO',
    'Barbade': 'HORS_EURO',
    'Belize': 'HORS_EURO',
    'Costa Rica': 'HORS_EURO',
    'Cuba': 'HORS_EURO',
    'Dominique': 'HORS_EURO',
    'République dominicaine': 'HORS_EURO',
    'Salvador': 'HORS_EURO',
    'Grenade': 'HORS_EURO',
    'Guatemala': 'HORS_EURO',
    'Haïti': 'HORS_EURO',
    'Honduras': 'HORS_EURO',
    'Jamaïque': 'HORS_EURO',
    'Nicaragua': 'HORS_EURO',
    'Panama': 'HORS_EURO',
    'Saint-Christophe-et-Niévès': 'HORS_EURO',
    'Sainte-Lucie': 'HORS_EURO',
    'Saint-Vincent-et-les-Grenadines': 'HORS_EURO',
    'Trinité-et-Tobago': 'HORS_EURO',
    
    // Amérique du Sud
    'Argentine': 'HORS_EURO',
    'Bolivie': 'HORS_EURO',
    'Brésil': 'HORS_EURO',
    'Chili': 'HORS_EURO',
    'Colombie': 'HORS_EURO',
    'Équateur': 'HORS_EURO',
    'Guyana': 'HORS_EURO',
    'Paraguay': 'HORS_EURO',
    'Pérou': 'HORS_EURO',
    'Suriname': 'HORS_EURO',
    'Uruguay': 'HORS_EURO',
    'Venezuela': 'HORS_EURO',
    
    // Océanie
    'Australie': 'HORS_EURO',
    'Fidji': 'HORS_EURO',
    'Kiribati': 'HORS_EURO',
    'Marshall': 'HORS_EURO',
    'Micronésie': 'HORS_EURO',
    'Nauru': 'HORS_EURO',
    'Nouvelle-Zélande': 'HORS_EURO',
    'Palaos': 'HORS_EURO',
    'Papouasie-Nouvelle-Guinée': 'HORS_EURO',
    'Salomon': 'HORS_EURO',
    'Samoa': 'HORS_EURO',
    'Tonga': 'HORS_EURO',
    'Tuvalu': 'HORS_EURO',
    'Vanuatu': 'HORS_EURO'
};

/**
 * Retourne la liste des pays triée alphabétiquement
 */
export function getCountriesList() {
    return Object.keys(COUNTRIES_DATA).sort();
}

/**
 * Retourne la zone d'un pays
 */
export function getCountryZone(country) {
    return COUNTRIES_DATA[country] || null;
}

/**
 * Filtre les pays selon une recherche
 */
export function filterCountries(search) {
    if (!search) return getCountriesList();
    
    const searchLower = search.toLowerCase();
    return getCountriesList().filter(country => 
        country.toLowerCase().includes(searchLower)
    );
}
