/**
 * Utilitaires pour la gestion du défilement automatique
 */

/**
 * Classe pour gérer le défilement automatique pendant le drag
 */
export class AutoScroller {
    constructor(options = {}) {
        this.speed = options.speed || 10;
        this.zone = options.zone || 50; // pixels depuis le bord pour déclencher le défilement
        this.interval = null;
        this.frameRate = options.frameRate || 16; // ~60fps
    }

    /**
     * Démarre le défilement automatique pour la fenêtre
     * @param {MouseEvent} event - Événement de souris
     */
    startWindowScroll(event) {
        this.stop();
        
        const viewportHeight = window.innerHeight;
        const mouseY = event.clientY;

        // Défilement vers le haut
        if (mouseY < this.zone) {
            this.interval = setInterval(() => {
                window.scrollBy(0, -this.speed);
            }, this.frameRate);
        }
        // Défilement vers le bas
        else if (mouseY > viewportHeight - this.zone) {
            this.interval = setInterval(() => {
                window.scrollBy(0, this.speed);
            }, this.frameRate);
        }
    }

    /**
     * Démarre le défilement automatique pour un conteneur
     * @param {MouseEvent} event - Événement de souris
     * @param {HTMLElement} container - Conteneur à faire défiler
     */
    startContainerScroll(event, container) {
        this.stop();
        
        const containerRect = container.getBoundingClientRect();
        const mouseY = event.clientY;

        // Défilement vers le haut dans le conteneur
        if (mouseY < containerRect.top + this.zone && mouseY > containerRect.top) {
            this.interval = setInterval(() => {
                container.scrollTop = Math.max(0, container.scrollTop - this.speed);
            }, this.frameRate);
        }
        // Défilement vers le bas dans le conteneur
        else if (mouseY > containerRect.bottom - this.zone && mouseY < containerRect.bottom) {
            this.interval = setInterval(() => {
                container.scrollTop = Math.min(
                    container.scrollHeight - container.clientHeight,
                    container.scrollTop + this.speed
                );
            }, this.frameRate);
        }
    }

    /**
     * Arrête le défilement automatique
     */
    stop() {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
    }

    /**
     * Vérifie si le défilement automatique est actif
     * @returns {boolean}
     */
    isActive() {
        return this.interval !== null;
    }
}

/**
 * Instance globale d'auto-scroller pour réutilisation
 */
export const globalAutoScroller = new AutoScroller();
