# Script PowerShell pour réinitialiser la base de données MySQL
Write-Host "🔄 Réinitialisation de la base de données OSI Manager..." -ForegroundColor Cyan

# Supprimer la base de données
Write-Host "📥 Suppression de la base de données..." -ForegroundColor Yellow
php bin/console doctrine:database:drop --force --if-exists

# Créer la base de données
Write-Host "📤 Création de la base de données..." -ForegroundColor Yellow
php bin/console doctrine:database:create

# Créer le schéma
Write-Host "🏗️ Création du schéma..." -ForegroundColor Yellow
php bin/console doctrine:schema:create

# Charger les données de test
Write-Host "📊 Chargement des données de test..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/load-test-data" -Method GET
    Write-Host $response.Content -ForegroundColor Green
} catch {
    Write-Host "⚠️ Erreur lors du chargement des données de test. Assurez-vous que le serveur est démarré." -ForegroundColor Red
}

Write-Host "✅ Base de données réinitialisée avec succès !" -ForegroundColor Green
Write-Host "🌐 Accédez à l'application : http://localhost:8000" -ForegroundColor Cyan
