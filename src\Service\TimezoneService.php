<?php

namespace App\Service;

class TimezoneService
{
    private const DEFAULT_TIMEZONE = 'Europe/Paris';

    public function convertToUserTimezone(\DateTime $dateTime, string $userTimezone = self::DEFAULT_TIMEZONE): \DateTime
    {
        $converted = clone $dateTime;
        $converted->setTimezone(new \DateTimeZone($userTimezone));
        return $converted;
    }

    public function convertFromUserTimezone(\DateTime $dateTime, string $userTimezone = self::DEFAULT_TIMEZONE): \DateTime
    {
        // Si la date n'a pas de timezone, on assume qu'elle est dans le timezone utilisateur
        if ($dateTime->getTimezone()->getName() === 'UTC') {
            $dateTime->setTimezone(new \DateTimeZone($userTimezone));
        }
        
        // Convertir en UTC pour le stockage
        $utc = clone $dateTime;
        $utc->setTimezone(new \DateTimeZone('UTC'));
        return $utc;
    }

    public function createFromISOString(string $isoString, string $userTimezone = self::DEFAULT_TIMEZONE): \DateTime
    {
        $dateTime = new \DateTime($isoString);
        
        // Si c'est une date ISO sans timezone (du formulaire HTML), 
        // on l'interprète comme étant dans le timezone utilisateur
        if (!str_contains($isoString, 'Z') && !str_contains($isoString, '+') && !str_contains($isoString, '-')) {
            $dateTime = new \DateTime($isoString, new \DateTimeZone($userTimezone));
        }
        
        return $dateTime;
    }
}
