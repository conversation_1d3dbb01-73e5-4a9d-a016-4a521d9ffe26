# Documentation des Composants Réutilisables

## Vue d'ensemble

Cette documentation répertorie tous les éléments réutilisables créés lors de la refactorisation du système utilisateurs. **Consultez cette liste avant de créer de nouveaux composants** pour éviter les doublons.

---

## 🛠️ Utilitaires JavaScript

### 1. **Throttling & Debouncing** (`assets/js/utils/throttle.js`)

**Quand utiliser :** Optimisation des performances pour événements fréquents

```javascript
import { throttle, debounce, throttleAnimationFrame } from '../js/utils/throttle.js';

// Throttle classique (limite la fréquence d'exécution)
const throttledFunction = throttle(myFunction, 300);

// Debounce (attend la fin des événements)
const debouncedSearch = debounce(searchFunction, 500);

// Throttle optimisé pour animations
const smoothHandler = throttleAnimationFrame(mouseMoveHandler);
```

**Cas d'usage :**
- ✅ Recherche en temps réel
- ✅ Événements de scroll/resize
- ✅ Mouvements de souris
- ✅ Validation de formulaires

---

### 2. **Calculs Géométriques** (`assets/js/utils/intersection.js`)

**Quand utiliser :** Détection de collisions, positionnement, coordonnées

```javascript
import {
    rectanglesIntersect,
    elementIntersectsSelection,
    viewportToPageCoordinates,
    viewportToContainerCoordinates
} from '../js/utils/intersection.js';

// Vérifier si deux rectangles se chevauchent
const overlap = rectanglesIntersect(rect1, rect2);

// Convertir coordonnées viewport → page
const pageCoords = viewportToPageCoordinates(clientX, clientY);

// Convertir coordonnées viewport → conteneur
const containerCoords = viewportToContainerCoordinates(clientX, clientY, container);
```

**Cas d'usage :**
- ✅ Drag & drop
- ✅ Sélection par zone
- ✅ Positionnement de tooltips
- ✅ Détection de survol

---

### 3. **Défilement Automatique** (`assets/js/utils/auto-scroll.js`)

**Quand utiliser :** Scroll automatique pendant les interactions

```javascript
import { AutoScroller, globalAutoScroller } from '../js/utils/auto-scroll.js';

// Instance globale (recommandée)
globalAutoScroller.startWindowScroll(event);
globalAutoScroller.startContainerScroll(event, container);
globalAutoScroller.stop();

// Instance personnalisée
const scroller = new AutoScroller({ speed: 15, zone: 60 });
```

**Cas d'usage :**
- ✅ Drag & drop avec scroll
- ✅ Sélection multi-éléments
- ✅ Réorganisation de listes
- ✅ Upload de fichiers par drag

---

### 4. **Sélection par Drag** (`assets/js/utils/drag-selection.js`)

**Quand utiliser :** Sélection multiple d'éléments par glisser-déposer

```javascript
import { DragSelection } from '../js/utils/drag-selection.js';

const selection = new DragSelection(container, {
    itemSelector: '.selectable-item',
    useContainer: false, // true pour modals
    autoScroll: true,
    onSelectionChange: (selectedItems) => {
        console.log('Sélectionnés:', selectedItems);
    }
});
```

**Cas d'usage :**
- ✅ Tableaux de données
- ✅ Galeries d'images
- ✅ Listes de fichiers
- ✅ Sélection d'utilisateurs

---

## 🎮 Controllers Stimulus

### 1. **Gestion d'Utilisateurs** (`assets/controllers/user_management_controller.js`)

**Quand utiliser :** CRUD d'utilisateurs, synchronisation LDAP

**Fonctionnalités :**
- ✅ Édition d'utilisateurs (modal)
- ✅ Suppression individuelle
- ✅ Synchronisation LDAP
- ✅ Gestion des formulaires

**Réutilisation :**
```html
<div data-controller="user-management"
     data-user-management-sync-url-value="/api/sync"
     data-user-management-delete-url-value="/api/users/__ID__">
```

---

### 2. **Sélection Principale** (`assets/controllers/main_selection_controller.js`)

**Quand utiliser :** Sélection multiple dans tableaux principaux

**Fonctionnalités :**
- ✅ Drag-to-select (coordonnées absolues)
- ✅ Bouton flottant selon scroll
- ✅ Suppression multiple
- ✅ Indicateurs de sélection

**Réutilisation :**
```html
<div data-controller="main-selection"
     data-main-selection-delete-url-value="/api/delete-multiple">
    <tbody data-main-selection-target="container">
        <!-- Éléments sélectionnables -->
    </tbody>
</div>
```

---

### 3. **Sélection Modale** (`assets/controllers/modal_selection_controller.js`)

**Quand utiliser :** Sélection multiple dans modals/popups

**Fonctionnalités :**
- ✅ Drag-to-select (coordonnées relatives)
- ✅ Recherche avec debounce
- ✅ Synchronisation checkboxes
- ✅ Ajout d'éléments

**Réutilisation :**
```html
<div data-controller="modal-selection"
     data-modal-selection-load-url-value="/api/load"
     data-modal-selection-add-url-value="/api/add">
    <div data-modal-selection-target="modal">
        <!-- Contenu modal -->
    </div>
</div>
```

---

### 4. **Gestion de Missions** (`assets/controllers/mission_management_controller.js`)

**Quand utiliser :** CRUD de missions, gestion des formulaires

**Fonctionnalités :**
- ✅ Édition de missions (modal)
- ✅ Suppression individuelle
- ✅ Création de missions
- ✅ Gestion des formulaires

**Réutilisation :**
```html
<div data-controller="mission-management"
     data-mission-management-show-url-value="/api/missions/__ID__"
     data-mission-management-delete-url-value="/api/missions/__ID__">
```

---

### 5. **Modals de Missions** (`assets/controllers/mission_modal_controller.js`)

**Quand utiliser :** Gestion des modals d'ajout/édition de missions

**Fonctionnalités :**
- ✅ Ouverture/fermeture de modals
- ✅ Chargement des clients/sites
- ✅ Gestion des formulaires

**Réutilisation :**
```html
<div data-controller="mission-modal"
     data-mission-modal-clients-url-value="/api/clients"
     data-mission-modal-sites-url-value="/api/clients/__ID__/sites">
```

---

### 6. **Sélection d'Utilisateurs pour Missions** (`assets/controllers/mission_user_selection_controller.js`)

**Quand utiliser :** Sélection d'utilisateurs spécifique aux missions

**Fonctionnalités :**
- ✅ Recherche avec debounce
- ✅ Sélection multiple
- ✅ Gestion création/édition
- ✅ Synchronisation avec formulaires

**Réutilisation :**
```html
<div data-controller="mission-user-selection"
     data-mission-user-selection-users-url-value="/api/users">
    <input data-mission-user-selection-target="createSearch">
    <div data-mission-user-selection-target="createContainer"></div>
</div>
```

---

## 🎨 Styles CSS Réutilisables

### Classes de Sélection

```css
/* Conteneur de sélection */
.user-selection-container {
    user-select: none;
}

/* Élément sélectionnable */
.user-item {
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-item:hover {
    background-color: #f3f4f6;
}

.user-item.selected {
    background-color: #dbeafe;
    border-left: 4px solid #3b82f6;
}

.user-item.selecting {
    background-color: #e0e7ff;
}

/* Boîte de sélection */
.drag-selection-box {
    position: absolute;
    border: 2px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
    pointer-events: none;
    z-index: 1000;
    border-radius: 4px;
}

/* État de drag */
.dragging {
    cursor: crosshair !important;
    user-select: none !important;
}
```

---

## 🔧 Fonctions Globales Disponibles

### Dans `templates/base.html.twig`

```javascript
// AJAX simplifié
window.ajax.get(url)
window.ajax.post(url, data)
window.ajax.put(url, data)
window.ajax.delete(url)

// Notifications
window.showToast.success(message)
window.showToast.error(message)
window.showToast.warning(message)

// Confirmations
window.showConfirm({
    title: 'Titre',
    message: 'Message',
    confirmText: 'OK',
    cancelText: 'Annuler',
    type: 'danger'
})

// Spinner de chargement
window.loadingSpinner.show(message)
window.loadingSpinner.hide()
```

---

## 📋 Checklist Avant Création

**Avant de créer un nouveau composant, vérifiez :**

### Pour les interactions utilisateur :
- [ ] Ai-je besoin de throttling/debouncing ? → `throttle.js`
- [ ] Ai-je besoin de calculs de position ? → `intersection.js`
- [ ] Ai-je besoin de scroll automatique ? → `auto-scroll.js`
- [ ] Ai-je besoin de sélection multiple ? → `drag-selection.js`

### Pour les controllers Stimulus :
- [ ] Est-ce de la gestion d'utilisateurs ? → `user_management_controller.js`
- [ ] Est-ce de la sélection dans un tableau ? → `main_selection_controller.js`
- [ ] Est-ce de la sélection dans un modal ? → `modal_selection_controller.js`

### Pour les styles :
- [ ] Ai-je besoin de styles de sélection ? → Classes `.user-item`, `.selected`, etc.
- [ ] Ai-je besoin d'une boîte de sélection ? → `.drag-selection-box`

### Pour les appels API :
- [ ] Puis-je utiliser `window.ajax` ? → Oui, toujours préférable
- [ ] Ai-je besoin de notifications ? → `window.showToast`
- [ ] Ai-je besoin de confirmations ? → `window.showConfirm`

---

## 🚀 Bonnes Pratiques

1. **Toujours importer** plutôt que recréer
2. **Étendre les classes** existantes si besoin
3. **Réutiliser les styles** CSS définis
4. **Utiliser les fonctions globales** pour AJAX/notifications
5. **Documenter** les nouveaux composants créés

Cette approche garantit un code **DRY** (Don't Repeat Yourself) et **maintenable** ! 🎯

---

## 📁 Structure des Fichiers Réutilisables

```
assets/
├── controllers/                    # Controllers Stimulus réutilisables
│   ├── main_selection_controller.js    # Sélection tableaux principaux
│   ├── modal_selection_controller.js   # Sélection dans modals
│   └── user_management_controller.js   # CRUD utilisateurs
│
├── js/utils/                      # Utilitaires JavaScript
│   ├── throttle.js                    # Throttling/debouncing
│   ├── intersection.js               # Calculs géométriques
│   ├── auto-scroll.js               # Défilement automatique
│   └── drag-selection.js            # Sélection par drag
│
└── bootstrap.js                   # Enregistrement des controllers
```

---

## 🔄 Workflow de Développement

### Avant de coder :
1. **Consulter cette documentation**
2. **Vérifier la checklist**
3. **Identifier les composants réutilisables**

### Pendant le développement :
1. **Importer les utilitaires** nécessaires
2. **Étendre les controllers** existants si possible
3. **Réutiliser les styles** CSS définis

### Après le développement :
1. **Documenter** les nouveaux composants créés
2. **Mettre à jour** cette documentation
3. **Tester** la réutilisabilité

---

## 💡 Exemples d'Extension

### Étendre un controller existant :

```javascript
import MainSelectionController from './main_selection_controller.js';

export default class extends MainSelectionController {
    connect() {
        super.connect();
        // Logique spécifique
    }

    // Surcharger une méthode
    updateUI() {
        super.updateUI();
        // Logique additionnelle
    }
}
```

### Créer un utilitaire basé sur les existants :

```javascript
import { DragSelection } from '../js/utils/drag-selection.js';
import { debounce } from '../js/utils/throttle.js';

export class AdvancedSelection extends DragSelection {
    constructor(container, options = {}) {
        super(container, options);
        this.setupAdvancedFeatures();
    }

    setupAdvancedFeatures() {
        // Fonctionnalités supplémentaires
    }
}
```

Cette documentation vous permettra de **maximiser la réutilisation** et d'éviter les doublons ! 📚

---

## ⚠️ Pièges à Éviter

### 1. **Targets Stimulus mal nommés**
```javascript
// ❌ Mauvais : containerTarget pointe sur <tbody> mais on fait querySelector('tbody')
const tableBody = this.containerTarget.querySelector('tbody'); // null !

// ✅ Bon : containerTarget EST déjà le <tbody>
this.containerTarget.innerHTML = newContent;
```

### 2. **Événements custom sans bubbling**
```javascript
// ❌ Mauvais : événement ne remonte pas
this.dispatch('myEvent');

// ✅ Bon : événement remonte dans le DOM
this.dispatch('myEvent', { bubbles: true });
```

### 3. **Échappement de quotes**
```javascript
// ❌ Mauvais : quote en trop
data-user-name="${name.replace(/'/g, "\\'")}'"

// ✅ Bon : échappement correct
data-user-name="${name.replace(/'/g, "\\'")}"
```

### 4. **CSS dupliqué**
```css
/* ❌ Éviter les règles dupliquées */
.container { user-select: none; }
.container { user-select: none; } /* Doublon ! */

/* ✅ Consolider les règles */
.container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
```

Ces corrections garantissent un code plus robuste et maintenable ! 🔧
