import { Controller } from '@hotwired/stimulus';

/**
 * Controller Stimulus pour la gestion des primes
 */
export default class extends Controller {
    static targets = [
        'userPrimes', 'zonePrimes', 'niveauPrimes', 'dateDebutPrimes', 'dateFinPrimes',
        'primesTableBody', 'noPrimesMessage', 'primesTable',
        'calculerButton', 'loadingSpinner', 'calculerText',
        'toggleMissionsZero', 'toggleMissionsZeroThumb', 'toggleMissionsZeroLabel', 'masquerMissionsZero'
    ];

    static values = {
        usersApiUrl: String,
        missionsApiUrl: String,
        missionPrimesUrl: String,
        primeDetailUrl: String
    };

    connect() {
        console.log('Contrôleur primes connecté');
        this.initializeToggle();
        this.setupInitialData();
    }

    disconnect() {
        // Nettoyer les ressources si nécessaire
        if (this.loadTimer) {
            clearTimeout(this.loadTimer);
        }
    }

    /**
     * Initialise le toggle pour masquer les missions à 0€
     */
    initializeToggle() {
        this.setToggleMissionsZero(true);
    }

    /**
     * Initialise les données de la page
     */
    async setupInitialData() {
        // Définir les dates du mois en cours
        this.definirDatesMoisEnCours();

        // Charger les utilisateurs
        await this.loadUsers();

        // Calculer automatiquement les primes du mois en cours
        await this.calculerPrimes();
    }

    /**
     * Action : Calculer les primes
     */
    async calculerPrimes() {
        this.showLoading();

        try {
            const userId = this.userPrimesTarget.value;
            const zone = this.zonePrimesTarget.value;
            const niveau = this.niveauPrimesTarget.value;
            const dateDebut = this.dateDebutPrimesTarget.value;
            const dateFin = this.dateFinPrimesTarget.value;

            console.log('Calcul des primes avec filtres:', { userId, zone, niveau, dateDebut, dateFin });

            // Étape 1 : Récupérer les missions avec les filtres
            const params = new URLSearchParams();
            if (userId) params.append('user', userId);
            if (zone) params.append('zone', zone);
            if (niveau) params.append('niveau', niveau);
            if (dateDebut) params.append('debut', dateDebut);
            if (dateFin) params.append('fin', dateFin);

            let missionsUrl = this.missionsApiUrlValue;
            if (params.toString()) {
                missionsUrl += '?' + params.toString();
            }

            const missionsResponse = await window.ajax.get(missionsUrl);

            console.log('Réponse API missions brute:', missionsResponse);

            // Extraire les missions selon le format de la réponse
            let missions;
            if (Array.isArray(missionsResponse)) {
                missions = missionsResponse;
            } else if (missionsResponse && missionsResponse.data && Array.isArray(missionsResponse.data)) {
                missions = missionsResponse.data;
            } else if (missionsResponse && Array.isArray(missionsResponse.missions)) {
                missions = missionsResponse.missions;
            } else {
                console.warn('Format de réponse inattendu pour les missions:', missionsResponse);
                missions = [];
            }

            console.log('Missions extraites:', missions);

            if (!Array.isArray(missions)) {
                console.error('Les missions ne sont pas un tableau:', missions);
                missions = [];
            }

            // Étape 2 : Calculer les primes pour chaque mission
            const primesData = [];
            let totalPrimes = 0;
            let totalUtilisateurs = 0;

            for (const mission of missions) {
                try {
                    const primeUrl = this.missionPrimesUrlValue.replace('MISSION_ID', mission.id);
                    const primeResponse = await window.ajax.get(primeUrl);
                    const primeData = primeResponse.data || primeResponse;

                    // Si la mission a des utilisateurs assignés, créer une ligne par utilisateur
                    if (primeData.primesParUtilisateur && primeData.primesParUtilisateur.length > 0) {
                        primeData.primesParUtilisateur.forEach(primeUser => {
                            const data = {
                                mission: mission,
                                user: primeUser.user,
                                prime: primeUser.prime,
                                heuresReelles: primeUser.heuresReelles || 0,
                                dureeJours: primeData.dureeJours,
                                dateDebut: primeData.dateDebut,
                                dateFin: primeData.dateFin
                            };
                            primesData.push(data);
                            totalPrimes += data.prime;
                            totalUtilisateurs++;
                        });
                    } else {
                        // Mission sans utilisateurs assignés
                        const data = {
                            mission: mission,
                            user: null,
                            prime: 0,
                            heuresReelles: 0,
                            dureeJours: primeData.dureeJours,
                            dateDebut: primeData.dateDebut,
                            dateFin: primeData.dateFin
                        };
                        primesData.push(data);
                    }
                } catch (error) {
                    console.error(`Erreur lors du calcul de la prime pour la mission ${mission.id}:`, error);
                }
            }

            console.log('Données primes calculées:', primesData);
            this.displayPrimesData(primesData, totalPrimes, totalUtilisateurs);

        } catch (error) {
            console.error('Erreur lors du calcul des primes:', error);
            this.showError('Erreur lors du calcul des primes');
            window.showToast?.error('Erreur lors du calcul des primes');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Action : Calculer les primes du mois en cours
     */
    async calculerPrimesMoisEnCours() {
        // Réinitialiser les filtres
        this.userPrimesTarget.value = '';
        this.zonePrimesTarget.value = '';
        this.niveauPrimesTarget.value = '';

        // Définir les dates du mois en cours
        this.definirDatesMoisEnCours();

        // Calculer les primes
        await this.calculerPrimes();
    }

    /**
     * Définit les dates du mois en cours
     */
    definirDatesMoisEnCours() {
        const maintenant = new Date();
        const annee = maintenant.getFullYear();
        const mois = maintenant.getMonth(); // 0-11

        // Premier jour du mois
        const premierJour = new Date(annee, mois, 1);
        const premierJourStr = premierJour.toISOString().split('T')[0];

        // Dernier jour du mois
        const dernierJour = new Date(annee, mois + 1, 0);
        const dernierJourStr = dernierJour.toISOString().split('T')[0];

        // Définir les valeurs dans les champs
        this.dateDebutPrimesTarget.value = premierJourStr;
        this.dateFinPrimesTarget.value = dernierJourStr;

        console.log(`Dates du mois en cours définies: ${premierJourStr} au ${dernierJourStr}`);
    }

    /**
     * Charge les utilisateurs depuis l'API
     */
    async loadUsers() {
        try {
            const response = await window.ajax.get(this.usersApiUrlValue);

            console.log('Réponse API users brute:', response);

            // Extraire les données selon le format de la réponse
            let users;
            if (Array.isArray(response)) {
                users = response;
            } else if (response && response.data && Array.isArray(response.data)) {
                users = response.data;
            } else if (response && Array.isArray(response.users)) {
                users = response.users;
            } else {
                console.warn('Format de réponse inattendu pour les utilisateurs:', response);
                users = [];
            }

            console.log('Utilisateurs extraits:', users);

            if (!Array.isArray(users)) {
                console.error('Les utilisateurs ne sont pas un tableau:', users);
                users = [];
            }

            // Remplir le select des utilisateurs
            const select = this.userPrimesTarget;

            // Garder l'option "Tous les utilisateurs"
            const defaultOption = select.querySelector('option[value=""]');
            select.innerHTML = '';
            if (defaultOption) {
                select.appendChild(defaultOption);
            }

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = user.nomComplet || `${user.prenom} ${user.nom}`;
                select.appendChild(option);
            });

            console.log(`${users.length} utilisateurs chargés dans le select`);

        } catch (error) {
            console.error('Erreur lors du chargement des utilisateurs:', error);
            window.showToast?.error('Erreur lors du chargement des utilisateurs');
        }
    }

    /**
     * Affiche les données des primes dans le tableau
     */
    displayPrimesData(primesData, totalPrimes = 0, totalUtilisateurs = 0) {
        if (!primesData || primesData.length === 0) {
            this.showNoPrimesMessage('Aucune mission trouvée pour les critères sélectionnés');
            return;
        }

        // Calculer les statistiques d'heures
        const totalHeuresReelles = primesData.reduce((sum, data) => sum + (data.heuresReelles || 0), 0);
        const totalJoursTheo = primesData.reduce((sum, data) => sum + (data.dureeJours || 0), 0);
        const totalHeuresTheo = totalJoursTheo * 8;
        const ratioGlobal = totalHeuresTheo > 0 ? (totalHeuresReelles / totalHeuresTheo * 100) : 0;

        // Afficher le résumé des statistiques
        this.updateStatistiques(totalPrimes, totalUtilisateurs || primesData.length, totalHeuresReelles, totalHeuresTheo, ratioGlobal);

        // Masquer le message "aucune donnée"
        this.noPrimesMessageTarget.style.display = 'none';
        this.primesTableTarget.style.display = 'table';

        // Vider le tableau
        this.primesTableBodyTarget.innerHTML = '';

        primesData.forEach(data => {
            const mission = data.mission;
            const user = data.user;

            // Nom de l'utilisateur ou "Non assigné"
            const nomUtilisateur = user ? `${user.prenom} ${user.nom}` : 'Non assigné';

            // Période de la mission
            const periode = data.dateDebut && data.dateFin
                ? `${data.dateDebut} au ${data.dateFin}`
                : 'Non définie';

            // Calculer le ratio heures réelles / durée théorique
            const dureeTheorique = data.dureeJours * 8; // 8h par jour théorique
            const ratioEffort = dureeTheorique > 0 ? (data.heuresReelles / dureeTheorique * 100) : 0;
            const colorRatio = ratioEffort > 100 ? 'text-red-600' : ratioEffort > 80 ? 'text-orange-600' : 'text-green-600';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${mission.titre || mission.nom}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${nomUtilisateur}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${mission.pays || 'N/A'}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${mission.zone === 'EURO' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">
                        ${mission.zone === 'EURO' ? 'Zone Euro' : 'Hors Zone Euro'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Niveau ${mission.niveau}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${periode}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${data.dureeJours || 0} jour(s)
                    <div class="text-xs text-gray-500">${dureeTheorique}h théo.</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div class="font-medium">${data.heuresReelles.toFixed(1)}h</div>
                    <div class="text-xs ${colorRatio}">${ratioEffort.toFixed(0)}% effort</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${data.prime.toFixed(2)}€</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="${this.primeDetailUrlValue.replace('MISSION_ID', mission.id)}" class="text-blue-600 hover:text-blue-900">Détail primes</a>
                </td>
            `;
            this.primesTableBodyTarget.appendChild(row);
        });

        console.log(`${primesData.length} missions affichées`);

        // Appliquer le filtre pour masquer les missions à 0€ si activé
        this.applyMissionsZeroFilter();
    }

    /**
     * Affiche le message "aucune donnée"
     */
    showNoPrimesMessage(message) {
        this.primesTableTarget.style.display = 'none';
        this.noPrimesMessageTarget.style.display = 'block';
        this.noPrimesMessageTarget.textContent = message;
    }

    /**
     * Affiche l'état de chargement
     */
    showLoading() {
        if (this.hasCalculerButtonTarget) {
            this.calculerButtonTarget.disabled = true;
        }
        if (this.hasLoadingSpinnerTarget) {
            this.loadingSpinnerTarget.classList.remove('hidden');
        }
        if (this.hasCalculerTextTarget) {
            this.calculerTextTarget.textContent = 'Calcul...';
        }
    }

    /**
     * Masque l'état de chargement
     */
    hideLoading() {
        if (this.hasCalculerButtonTarget) {
            this.calculerButtonTarget.disabled = false;
        }
        if (this.hasLoadingSpinnerTarget) {
            this.loadingSpinnerTarget.classList.add('hidden');
        }
        if (this.hasCalculerTextTarget) {
            this.calculerTextTarget.textContent = 'Calculer';
        }
    }

    /**
     * Action : Toggle pour masquer/afficher les missions à 0€
     */
    toggleMasquerMissionsZero() {
        const currentValue = this.masquerMissionsZeroTarget.value === 'true';
        this.setToggleMissionsZero(!currentValue);

        // Réappliquer le filtrage sur les données actuelles
        this.applyMissionsZeroFilter();
    }

    /**
     * Configure l'état du toggle missions à 0€
     */
    setToggleMissionsZero(masquer) {
        if (!this.hasToggleMissionsZeroTarget) return;

        this.masquerMissionsZeroTarget.value = masquer.toString();

        if (masquer) {
            this.toggleMissionsZeroTarget.classList.remove('bg-gray-200');
            this.toggleMissionsZeroTarget.classList.add('bg-blue-600');
            this.toggleMissionsZeroThumbTarget.classList.remove('translate-x-0');
            this.toggleMissionsZeroThumbTarget.classList.add('translate-x-5');
            this.toggleMissionsZeroLabelTarget.textContent = 'Masquer les missions à 0€';
        } else {
            this.toggleMissionsZeroTarget.classList.remove('bg-blue-600');
            this.toggleMissionsZeroTarget.classList.add('bg-gray-200');
            this.toggleMissionsZeroThumbTarget.classList.remove('translate-x-5');
            this.toggleMissionsZeroThumbTarget.classList.add('translate-x-0');
            this.toggleMissionsZeroLabelTarget.textContent = 'Afficher toutes les missions';
        }
    }

    /**
     * Applique le filtre pour masquer/afficher les missions à 0€
     */
    applyMissionsZeroFilter() {
        if (!this.hasPrimesTableBodyTarget) return;

        const masquer = this.masquerMissionsZeroTarget.value === 'true';
        const rows = this.primesTableBodyTarget.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const primeCell = row.cells[8]; // Colonne "Prime individuelle"
            if (primeCell) {
                const primeText = primeCell.textContent.trim();
                const primeValue = parseFloat(primeText.replace('€', ''));

                if (masquer && primeValue === 0) {
                    row.style.display = 'none';
                } else {
                    row.style.display = '';
                    visibleCount++;
                }
            }
        });

        // Afficher/masquer le message "aucune donnée" si nécessaire
        if (visibleCount === 0) {
            this.showNoPrimesMessage('Aucune mission avec des primes > 0€ pour les critères sélectionnés');
        } else if (this.noPrimesMessageTarget.style.display !== 'none') {
            this.noPrimesMessageTarget.style.display = 'none';
            this.primesTableTarget.style.display = 'table';
        }
    }

    /**
     * Met à jour les statistiques affichées
     */
    updateStatistiques(totalPrimes, nombreMissions, totalHeuresReelles, totalHeuresTheo, ratioGlobal) {
        // Afficher le résumé
        const resumePrimes = document.getElementById('resumePrimes');
        if (resumePrimes) {
            resumePrimes.style.display = 'grid';
        }

        const totalPrimesElement = document.getElementById('totalPrimes');
        if (totalPrimesElement) {
            totalPrimesElement.textContent = totalPrimes.toFixed(2) + '€';
        }

        const nombreMissionsElement = document.getElementById('nombreMissions');
        if (nombreMissionsElement) {
            nombreMissionsElement.textContent = `${nombreMissions} assignations`;
        }

        const primeMoyenneElement = document.getElementById('primeMoyenne');
        if (primeMoyenneElement) {
            primeMoyenneElement.textContent = `${totalHeuresReelles.toFixed(0)}h réelles / ${totalHeuresTheo}h théo. (${ratioGlobal.toFixed(0)}%)`;
        }
    }

    /**
     * Affiche une erreur
     */
    showError(message) {
        this.showNoPrimesMessage(message);
    }
}
