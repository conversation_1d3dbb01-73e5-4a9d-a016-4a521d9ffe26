<?php

namespace App\Controller;

use App\Entity\User;
use App\Service\LdapService;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/api/ldap-sync', name: 'api_ldap_sync_')]
class LdapSyncController extends AbstractController
{
    public function __construct(
        private LdapService $ldapService,
        private UserRepository $userRepository,
        private EntityManagerInterface $entityManager
    ) {}

    #[Route('/load-all-users', name: 'load_all_users', methods: ['POST'])]
    public function loadAllUsers(): JsonResponse
    {
        try {
            $this->userRepository->initializeUsers();

            return $this->json([
                'success' => true,
                'message' => 'Synchronisation terminée'
            ], Response::HTTP_OK);
        } catch (\Throwable $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur lors de la synchronisation LDAP : ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/available-users', name: 'available_users', methods: ['GET'])]
    public function getAvailableUsers(): JsonResponse
    {
        // Récupérer les utilisateurs non-OSI pour la sélection
        $users = $this->userRepository->findNonOsiUsers();
        
        return $this->json($users, 200, [], ['groups' => ['user:read']]);
    }

    #[Route('/add-users', name: 'add_users', methods: ['POST'])]
    public function addUsersToOsi(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $userIds = $data['userIds'] ?? [];

        if (empty($userIds)) {
            return $this->json(['success' => false, 'message' => 'Aucun utilisateur sélectionné'], 400);
        }

        try {
            $count = 0;
            foreach ($userIds as $userId) {
                $user = $this->userRepository->find($userId);
                if ($user) {
                    $user->setUserOsi(true);
                    $count++;
                }
            }

            $this->entityManager->flush();

            return $this->json([
                'success' => true,
                'message' => "{$count} utilisateur(s) ajouté(s) à OSI",
                'count' => $count
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur lors de l\'ajout des utilisateurs : ' . $e->getMessage()
            ], 500);
        }
    }

    private function normalizeDepartment(string $department): string
    {
        // Remplace les espaces ou caractères spéciaux par des underscores
        $normalized = preg_replace('/[^A-Z0-9]/', '_', strtoupper($department));
        return $normalized;
    }
}
